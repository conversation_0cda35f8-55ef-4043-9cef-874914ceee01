# 用户引导系统测试指南

## 🎯 测试目标

验证用户引导系统在主应用中的完整集成，确保所有功能正常工作。

## 📋 测试清单

### ✅ 基础功能测试

#### 1. 系统初始化
- [ ] UserGuide 对象已加载
- [ ] Driver.js 库已加载
- [ ] AccessibilityEnhancer 已初始化
- [ ] 引导步骤配置正确（7个步骤）

#### 2. 目标元素检查
- [ ] 侧边栏 (`.sidebar`) 可见
- [ ] AI助手菜单 (`#ai-assistant-menu`) 可见
- [ ] 中央输入框 (`.central-input-container`) 可见
- [ ] 通知中心 (`.notification-container`) 可见
- [ ] 用户资料区域 (`#user-profile-sidebar`) 可见

### 🖱️ 交互功能测试

#### 3. 用户菜单按钮
- [ ] 侧边栏新手引导按钮 (`#sidebar-user-guide-item`) 存在
- [ ] 顶部新手引导按钮 (`#header-user-guide-item`) 存在
- [ ] 侧边栏退出登录按钮 (`#sidebar-logout-item`) 存在
- [ ] 顶部退出登录按钮 (`#logout-item`) 存在

#### 4. 菜单交互
- [ ] 点击侧边栏用户头像显示下拉菜单
- [ ] 点击顶部用户头像显示下拉菜单
- [ ] 点击新手引导按钮启动引导流程
- [ ] 点击退出登录按钮切换到登录页面

### 🎪 引导流程测试

#### 5. 引导步骤验证
- [ ] 步骤1：欢迎页面（body）
- [ ] 步骤2：侧边栏介绍（.sidebar）
- [ ] 步骤3：AI助手介绍（#ai-assistant-menu）
- [ ] 步骤4：输入框介绍（.central-input-container）
- [ ] 步骤5：通知中心介绍（.notification-container）
- [ ] 步骤6：用户资料介绍（#user-profile-sidebar）
- [ ] 步骤7：完成页面（body）

#### 6. 引导控制
- [ ] 可以正常启动引导
- [ ] 可以跳过引导
- [ ] 可以重置引导状态
- [ ] 引导完成后状态正确保存

### ♿ 可访问性测试

#### 7. 键盘导航
- [ ] ESC键可以退出引导
- [ ] 方向键可以导航步骤
- [ ] Tab键可以在按钮间切换
- [ ] Enter键可以确认操作

#### 8. 屏幕阅读器支持
- [ ] 引导内容有正确的ARIA标签
- [ ] 步骤变化时有语音公告
- [ ] 按钮有适当的标签

## 🧪 测试方法

### 方法1：使用快速测试页面
1. 打开 `user-guide-quick-test.html`
2. 按顺序点击测试按钮
3. 查看测试结果和状态

### 方法2：使用浏览器控制台
```javascript
// 运行基础测试
UserGuideTest.runAllTests()

// 运行集成测试
UserGuideIntegrationTest.runIntegrationTests()

// 测试菜单交互
UserGuideIntegrationTest.testUserMenuInteraction()

// 启用调试模式
UserGuideTest.enableDebugMode()

// 手动启动引导
UserGuide.start()

// 重置引导状态
UserGuide.reset()
```

### 方法3：手动测试步骤
1. **打开主应用** (`index.html`)
2. **测试侧边栏菜单**：
   - 点击侧边栏用户头像
   - 验证下拉菜单显示
   - 点击"新手引导"选项
   - 验证引导是否启动
3. **测试顶部菜单**：
   - 点击顶部用户头像
   - 验证下拉菜单显示
   - 点击"新手引导"选项
   - 验证引导是否启动
4. **测试引导流程**：
   - 完整走完7个引导步骤
   - 验证每个步骤的目标元素正确高亮
   - 测试"下一步"、"上一步"、"跳过"按钮
5. **测试退出登录**：
   - 点击退出登录按钮
   - 验证是否切换到登录页面

## 🔧 常见问题排查

### 问题1：新手引导按钮没有响应
**可能原因**：
- 事件绑定失败
- UserGuide对象未初始化
- 按钮元素不存在

**解决方法**：
```javascript
// 检查按钮是否存在
console.log(document.getElementById('sidebar-user-guide-item'))
console.log(document.getElementById('header-user-guide-item'))

// 重新绑定事件
ensureGuideButtonsBinding()

// 检查UserGuide状态
console.log(UserGuide.isInitialized)
```

### 问题2：引导步骤目标元素不可见
**可能原因**：
- 元素选择器错误
- 元素被CSS隐藏
- 元素尚未加载

**解决方法**：
```javascript
// 检查目标元素
const elements = ['.sidebar', '#ai-assistant-menu', '.central-input-container', '.notification-container', '#user-profile-sidebar']
elements.forEach(selector => {
    const el = document.querySelector(selector)
    console.log(selector, el, el ? 'visible' : 'not found')
})
```

### 问题3：退出登录功能不工作
**可能原因**：
- showLoginPage函数未定义
- 登录页面元素不存在
- 事件绑定失败

**解决方法**：
```javascript
// 检查函数是否存在
console.log(typeof showLoginPage)

// 检查登录页面元素
console.log(document.getElementById('login-page'))

// 手动测试退出登录
showLoginPage()
```

### 问题4：引导系统未初始化
**可能原因**：
- Driver.js库未加载
- 初始化时机过早
- 网络加载失败

**解决方法**：
```javascript
// 检查依赖
console.log(typeof Driver)
console.log(typeof UserGuide)

// 手动初始化
UserGuide.init()

// 快速修复
UserGuideIntegrationTest.quickFix()
```

## 📊 测试报告模板

### 测试环境
- 浏览器：[Chrome/Firefox/Safari/Edge]
- 版本：[浏览器版本]
- 操作系统：[Windows/macOS/Linux]
- 屏幕分辨率：[分辨率]

### 测试结果
- 基础功能：✅/❌ [通过/失败数量]
- 交互功能：✅/❌ [通过/失败数量]
- 引导流程：✅/❌ [通过/失败数量]
- 可访问性：✅/❌ [通过/失败数量]

### 发现的问题
1. [问题描述]
   - 重现步骤：[步骤]
   - 预期结果：[预期]
   - 实际结果：[实际]
   - 严重程度：[高/中/低]

### 修复建议
1. [修复建议1]
2. [修复建议2]

## 🚀 部署前检查

在将用户引导系统部署到生产环境前，请确保：

- [ ] 所有测试用例通过
- [ ] 在不同浏览器中测试正常
- [ ] 在不同设备尺寸下显示正确
- [ ] 可访问性功能正常工作
- [ ] 性能影响在可接受范围内
- [ ] 错误处理机制完善
- [ ] 用户体验流畅自然

## 📞 技术支持

如果在测试过程中遇到问题：

1. 查看浏览器控制台错误信息
2. 运行自动化测试工具
3. 参考本文档的问题排查部分
4. 检查网络连接和资源加载情况

---

**最后更新**：2024年6月18日  
**测试版本**：用户引导系统 v1.0.0
