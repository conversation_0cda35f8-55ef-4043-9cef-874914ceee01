<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户引导功能演示</title>
    <style>
        body {
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .demo-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .demo-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .demo-title {
            font-size: 32px;
            color: #333;
            margin: 0 0 10px 0;
            font-weight: 600;
        }
        .demo-subtitle {
            font-size: 16px;
            color: #666;
            margin: 0;
        }
        .demo-section {
            margin-bottom: 30px;
        }
        .demo-section h3 {
            color: #2196f3;
            font-size: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .demo-buttons {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            margin-bottom: 20px;
        }
        .demo-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .demo-btn-primary {
            background: #2196f3;
            color: white;
        }
        .demo-btn-primary:hover {
            background: #1976d2;
            transform: translateY(-1px);
        }
        .demo-btn-secondary {
            background: #f0f0f0;
            color: #333;
            border: 1px solid #ddd;
        }
        .demo-btn-secondary:hover {
            background: #e0e0e0;
        }
        .demo-btn-success {
            background: #4caf50;
            color: white;
        }
        .demo-btn-success:hover {
            background: #45a049;
        }
        .demo-btn-warning {
            background: #ff9800;
            color: white;
        }
        .demo-btn-warning:hover {
            background: #f57c00;
        }
        .demo-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .demo-info h4 {
            margin: 0 0 8px 0;
            color: #1976d2;
            font-size: 16px;
        }
        .demo-info p {
            margin: 0;
            color: #333;
            font-size: 14px;
        }
        .demo-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .demo-feature {
            background: #f9f9f9;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        .demo-feature h4 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 16px;
        }
        .demo-feature p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }
        .demo-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        .status-ready {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status-loading {
            background: #fff3e0;
            color: #f57c00;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
            color: #666;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .demo-container {
                padding: 20px;
                margin: 10px;
            }
            .demo-buttons {
                flex-direction: column;
            }
            .demo-btn {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1 class="demo-title">🎯 用户引导功能演示</h1>
            <p class="demo-subtitle">体验完整的新手引导系统</p>
        </div>

        <div class="demo-section">
            <h3>🚀 快速体验</h3>
            <div class="demo-buttons">
                <button class="demo-btn demo-btn-primary" onclick="startGuideDemo()">
                    ▶️ 开始引导演示
                </button>
                <button class="demo-btn demo-btn-secondary" onclick="resetGuideDemo()">
                    🔄 重置引导状态
                </button>
                <a href="index.html" class="demo-btn demo-btn-success">
                    🏠 返回主应用
                </a>
            </div>
            
            <div class="demo-info">
                <h4>💡 使用说明</h4>
                <p>点击"开始引导演示"按钮体验完整的用户引导流程。引导系统会自动检测您的设备类型并适配相应的显示效果。</p>
            </div>
        </div>

        <div class="demo-section">
            <h3>⚙️ 功能控制</h3>
            <div class="demo-buttons">
                <button class="demo-btn demo-btn-secondary" onclick="enableDebugMode()">
                    🔧 启用调试模式
                </button>
                <button class="demo-btn demo-btn-secondary" onclick="runAllTests()">
                    🧪 运行测试套件
                </button>
                <button class="demo-btn demo-btn-warning" onclick="simulateScenarios()">
                    🎭 模拟用户场景
                </button>
            </div>
        </div>

        <div class="demo-section">
            <h3>✨ 功能特性</h3>
            <div class="demo-features">
                <div class="demo-feature">
                    <h4>🎯 智能引导</h4>
                    <p>7个精心设计的引导步骤，覆盖平台核心功能</p>
                </div>
                <div class="demo-feature">
                    <h4>📱 响应式设计</h4>
                    <p>完美适配桌面、平板、手机等不同设备</p>
                </div>
                <div class="demo-feature">
                    <h4>♿ 可访问性</h4>
                    <p>支持键盘导航和屏幕阅读器</p>
                </div>
                <div class="demo-feature">
                    <h4>🎨 主题集成</h4>
                    <p>无缝融入项目的设计系统</p>
                </div>
                <div class="demo-feature">
                    <h4>💾 智能存储</h4>
                    <p>记住用户状态，避免重复显示</p>
                </div>
                <div class="demo-feature">
                    <h4>🔧 开发友好</h4>
                    <p>完整的测试工具和调试功能</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h3>📊 系统状态</h3>
            <div id="system-status">
                <p>正在检查系统状态...</p>
            </div>
        </div>

        <div class="footer">
            <p>🛠️ 跨境运营助手 - 用户引导系统 v1.0.0</p>
            <p>基于 Driver.js 构建，支持现代浏览器</p>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="libs/driver.js/driver.min.js"></script>
    <link rel="stylesheet" href="libs/driver.js/driver.min.css">
    <link rel="stylesheet" href="user-guide.css">
    <script src="user-guide.js"></script>
    <script src="user-guide-test.js"></script>

    <script>
        // 演示页面专用脚本
        let demoGuide = null;

        // 初始化演示
        document.addEventListener('DOMContentLoaded', function() {
            initDemo();
            updateSystemStatus();
        });

        function initDemo() {
            // 创建演示用的引导步骤
            const demoSteps = [
                {
                    element: '.demo-header',
                    popover: {
                        title: '🎯 欢迎来到引导演示！',
                        description: '这是一个专门的演示页面，展示用户引导系统的各项功能。',
                        position: 'bottom'
                    }
                },
                {
                    element: '.demo-buttons',
                    popover: {
                        title: '🚀 控制按钮',
                        description: '使用这些按钮来控制引导系统的行为，体验不同的功能。',
                        position: 'bottom'
                    }
                },
                {
                    element: '.demo-features',
                    popover: {
                        title: '✨ 功能特性',
                        description: '这里展示了用户引导系统的主要特性和优势。',
                        position: 'top'
                    }
                },
                {
                    element: '#system-status',
                    popover: {
                        title: '📊 系统状态',
                        description: '实时显示引导系统的运行状态和配置信息。',
                        position: 'top'
                    }
                }
            ];

            // 初始化演示引导
            if (typeof Driver !== 'undefined') {
                demoGuide = new Driver({
                    className: 'user-guide-driver demo-guide',
                    animate: true,
                    opacity: 0.75,
                    padding: 10,
                    allowClose: true,
                    doneBtnText: '完成演示',
                    closeBtnText: '跳过',
                    nextBtnText: '下一步',
                    prevBtnText: '上一步'
                });
                demoGuide.defineSteps(demoSteps);
            }
        }

        function startGuideDemo() {
            if (demoGuide) {
                demoGuide.start();
            } else {
                alert('引导系统未正确加载，请刷新页面重试');
            }
        }

        function resetGuideDemo() {
            if (typeof UserGuide !== 'undefined') {
                UserGuide.reset();
                updateSystemStatus();
                alert('引导状态已重置');
            }
        }

        function enableDebugMode() {
            if (typeof UserGuideTest !== 'undefined') {
                UserGuideTest.enableDebugMode();
            } else {
                alert('调试工具未加载');
            }
        }

        function runAllTests() {
            if (typeof UserGuideTest !== 'undefined') {
                UserGuideTest.runAllTests().then(() => {
                    alert('测试完成，请查看控制台输出');
                });
            } else {
                alert('测试工具未加载');
            }
        }

        function simulateScenarios() {
            if (typeof UserGuideTest !== 'undefined') {
                UserGuideTest.simulateUserScenarios();
                alert('用户场景已加载，请查看控制台说明');
            } else {
                alert('测试工具未加载');
            }
        }

        function updateSystemStatus() {
            const statusDiv = document.getElementById('system-status');
            let statusHTML = '';

            // 检查各个组件的状态
            const checks = [
                { name: 'Driver.js库', status: typeof Driver !== 'undefined' },
                { name: '用户引导管理器', status: typeof UserGuide !== 'undefined' },
                { name: '可访问性增强器', status: typeof AccessibilityEnhancer !== 'undefined' },
                { name: '存储管理器', status: typeof GuideStorage !== 'undefined' },
                { name: '测试工具', status: typeof UserGuideTest !== 'undefined' }
            ];

            statusHTML += '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;">';
            
            checks.forEach(check => {
                const statusClass = check.status ? 'status-ready' : 'status-loading';
                const statusText = check.status ? '✅ 就绪' : '❌ 未加载';
                statusHTML += `
                    <div style="padding: 8px 12px; background: #f9f9f9; border-radius: 6px; font-size: 14px;">
                        <strong>${check.name}</strong>
                        <span class="demo-status ${statusClass}">${statusText}</span>
                    </div>
                `;
            });

            statusHTML += '</div>';

            // 添加引导状态信息
            if (typeof UserGuide !== 'undefined') {
                const stats = UserGuide.getStats ? UserGuide.getStats() : {};
                statusHTML += `
                    <div style="margin-top: 15px; padding: 12px; background: #e3f2fd; border-radius: 6px;">
                        <strong>引导状态:</strong> ${stats.isCompleted ? '已完成' : '未完成'} | 
                        <strong>版本:</strong> ${stats.version || 'N/A'} | 
                        <strong>步骤数:</strong> ${stats.totalSteps || 0}
                    </div>
                `;
            }

            statusDiv.innerHTML = statusHTML;
        }

        // 定期更新状态
        setInterval(updateSystemStatus, 5000);
    </script>
</body>
</html>
