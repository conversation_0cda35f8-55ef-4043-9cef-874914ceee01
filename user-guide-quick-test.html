<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户引导功能快速测试</title>
    <style>
        body {
            font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        .test-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        .test-title {
            font-size: 28px;
            color: #333;
            margin: 0 0 10px 0;
            font-weight: 600;
        }
        .test-subtitle {
            font-size: 16px;
            color: #666;
            margin: 0;
        }
        .test-section {
            margin-bottom: 30px;
        }
        .test-section h3 {
            color: #2196f3;
            font-size: 18px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 20px;
        }
        .test-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .test-btn-primary {
            background: #2196f3;
            color: white;
        }
        .test-btn-primary:hover {
            background: #1976d2;
            transform: translateY(-1px);
        }
        .test-btn-success {
            background: #4caf50;
            color: white;
        }
        .test-btn-success:hover {
            background: #45a049;
        }
        .test-btn-warning {
            background: #ff9800;
            color: white;
        }
        .test-btn-warning:hover {
            background: #f57c00;
        }
        .test-btn-danger {
            background: #f44336;
            color: white;
        }
        .test-btn-danger:hover {
            background: #d32f2f;
        }
        .test-results {
            background: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
        .test-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 8px;
        }
        .status-loading {
            background: #fff3e0;
            color: #f57c00;
        }
        .status-success {
            background: #e8f5e8;
            color: #2e7d32;
        }
        .status-error {
            background: #ffebee;
            color: #c62828;
        }
        .test-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 16px;
            margin: 20px 0;
        }
        .test-info h4 {
            margin: 0 0 8px 0;
            color: #1976d2;
            font-size: 16px;
        }
        .test-info p {
            margin: 0;
            color: #333;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .test-container {
                padding: 20px;
                margin: 10px;
            }
            .test-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">🧪 用户引导功能快速测试</h1>
            <p class="test-subtitle">验证主应用中的用户引导系统集成</p>
        </div>

        <div class="test-section">
            <h3>🚀 基础功能测试</h3>
            <div class="test-buttons">
                <button class="test-btn test-btn-primary" onclick="runBasicTests()">
                    🔍 运行基础测试
                </button>
                <button class="test-btn test-btn-success" onclick="testGuideSystem()">
                    🎯 测试引导系统
                </button>
                <button class="test-btn test-btn-warning" onclick="testMenuButtons()">
                    📱 测试菜单按钮
                </button>
                <button class="test-btn test-btn-primary" onclick="testTargetElements()">
                    🎪 测试目标元素
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>🎮 交互功能测试</h3>
            <div class="test-buttons">
                <button class="test-btn test-btn-success" onclick="startUserGuide()">
                    ▶️ 启动用户引导
                </button>
                <button class="test-btn test-btn-warning" onclick="resetUserGuide()">
                    🔄 重置引导状态
                </button>
                <button class="test-btn test-btn-primary" onclick="testMenuInteraction()">
                    🖱️ 测试菜单交互
                </button>
                <button class="test-btn test-btn-danger" onclick="testLogoutFunction()">
                    🚪 测试退出登录
                </button>
            </div>
        </div>

        <div class="test-section">
            <h3>🔧 高级测试</h3>
            <div class="test-buttons">
                <button class="test-btn test-btn-primary" onclick="runIntegrationTests()">
                    🧪 完整集成测试
                </button>
                <button class="test-btn test-btn-warning" onclick="enableDebugMode()">
                    🔧 启用调试模式
                </button>
                <button class="test-btn test-btn-success" onclick="quickFix()">
                    ⚡ 快速修复
                </button>
                <a href="index.html" class="test-btn test-btn-primary">
                    🏠 返回主应用
                </a>
            </div>
        </div>

        <div class="test-info">
            <h4>💡 测试说明</h4>
            <p>这个页面用于快速验证用户引导系统在主应用中的集成情况。请按顺序执行测试，确保所有功能正常工作。</p>
        </div>

        <div class="test-section">
            <h3>📊 测试结果</h3>
            <div id="test-status">
                <span class="test-status status-loading">等待测试...</span>
            </div>
            <div class="test-results" id="test-results">
                <div>🔄 准备运行测试...</div>
                <div>💡 点击上方按钮开始测试各项功能</div>
            </div>
        </div>
    </div>

    <script>
        // 测试结果显示区域
        const testResults = document.getElementById('test-results');
        const testStatus = document.getElementById('test-status');

        // 添加测试日志
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.marginBottom = '4px';
            
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            logEntry.innerHTML = `[${timestamp}] ${icon} ${message}`;
            
            testResults.appendChild(logEntry);
            testResults.scrollTop = testResults.scrollHeight;
        }

        // 更新测试状态
        function updateStatus(message, type = 'loading') {
            testStatus.innerHTML = `<span class="test-status status-${type}">${message}</span>`;
        }

        // 清空测试结果
        function clearResults() {
            testResults.innerHTML = '';
            updateStatus('运行中...', 'loading');
        }

        // 基础测试
        function runBasicTests() {
            clearResults();
            addLog('开始运行基础功能测试...');
            
            // 检查必要的全局对象
            const checks = [
                { name: 'UserGuide', obj: window.UserGuide },
                { name: 'Driver.js', obj: window.Driver },
                { name: 'AccessibilityEnhancer', obj: window.AccessibilityEnhancer },
                { name: 'UserGuideTest', obj: window.UserGuideTest },
                { name: 'UserGuideIntegrationTest', obj: window.UserGuideIntegrationTest }
            ];

            let passed = 0;
            checks.forEach(check => {
                if (check.obj) {
                    addLog(`${check.name} 已加载`, 'success');
                    passed++;
                } else {
                    addLog(`${check.name} 未加载`, 'error');
                }
            });

            updateStatus(`基础测试完成: ${passed}/${checks.length}`, passed === checks.length ? 'success' : 'error');
        }

        // 测试引导系统
        function testGuideSystem() {
            clearResults();
            addLog('测试用户引导系统...');
            
            if (typeof UserGuide !== 'undefined') {
                addLog('UserGuide 对象存在', 'success');
                
                if (UserGuide.isInitialized) {
                    addLog('引导系统已初始化', 'success');
                } else {
                    addLog('引导系统未初始化，尝试初始化...', 'warning');
                    UserGuide.init().then(() => {
                        addLog('引导系统初始化成功', 'success');
                    }).catch(err => {
                        addLog(`引导系统初始化失败: ${err.message}`, 'error');
                    });
                }
                
                const steps = UserGuide.getGuideSteps();
                addLog(`引导步骤数量: ${steps.length}`, 'info');
                
                updateStatus('引导系统测试完成', 'success');
            } else {
                addLog('UserGuide 对象不存在', 'error');
                updateStatus('引导系统测试失败', 'error');
            }
        }

        // 测试菜单按钮
        function testMenuButtons() {
            clearResults();
            addLog('测试用户菜单按钮...');
            
            const buttons = [
                'sidebar-user-guide-item',
                'header-user-guide-item',
                'sidebar-logout-item',
                'logout-item'
            ];

            let found = 0;
            buttons.forEach(buttonId => {
                const button = document.getElementById(buttonId);
                if (button) {
                    addLog(`按钮 ${buttonId} 存在`, 'success');
                    found++;
                } else {
                    addLog(`按钮 ${buttonId} 不存在`, 'error');
                }
            });

            updateStatus(`菜单按钮测试完成: ${found}/${buttons.length}`, found === buttons.length ? 'success' : 'warning');
        }

        // 测试目标元素
        function testTargetElements() {
            clearResults();
            addLog('测试引导目标元素...');
            
            const elements = [
                { selector: '.sidebar', name: '侧边栏' },
                { selector: '#ai-assistant-menu', name: 'AI助手菜单' },
                { selector: '.central-input-container', name: '中央输入框' },
                { selector: '.notification-container', name: '通知中心' },
                { selector: '#user-profile-sidebar', name: '用户资料' }
            ];

            let visible = 0;
            elements.forEach(element => {
                const el = document.querySelector(element.selector);
                if (el) {
                    const isVisible = el.offsetWidth > 0 && el.offsetHeight > 0;
                    if (isVisible) {
                        addLog(`${element.name} 可见`, 'success');
                        visible++;
                    } else {
                        addLog(`${element.name} 存在但不可见`, 'warning');
                    }
                } else {
                    addLog(`${element.name} 不存在`, 'error');
                }
            });

            updateStatus(`目标元素测试完成: ${visible}/${elements.length}`, visible === elements.length ? 'success' : 'warning');
        }

        // 启动用户引导
        function startUserGuide() {
            clearResults();
            addLog('启动用户引导...');
            
            if (typeof UserGuide !== 'undefined' && UserGuide.start) {
                UserGuide.start();
                addLog('用户引导已启动', 'success');
                updateStatus('引导已启动', 'success');
            } else {
                addLog('无法启动用户引导', 'error');
                updateStatus('启动失败', 'error');
            }
        }

        // 重置用户引导
        function resetUserGuide() {
            clearResults();
            addLog('重置用户引导状态...');
            
            if (typeof UserGuide !== 'undefined' && UserGuide.reset) {
                UserGuide.reset();
                addLog('用户引导状态已重置', 'success');
                updateStatus('重置完成', 'success');
            } else {
                addLog('无法重置用户引导', 'error');
                updateStatus('重置失败', 'error');
            }
        }

        // 测试菜单交互
        function testMenuInteraction() {
            clearResults();
            addLog('测试用户菜单交互...');
            
            if (typeof UserGuideIntegrationTest !== 'undefined') {
                UserGuideIntegrationTest.testUserMenuInteraction();
                addLog('菜单交互测试已启动，请查看控制台', 'info');
                updateStatus('交互测试进行中', 'loading');
            } else {
                addLog('集成测试工具未加载', 'error');
                updateStatus('测试失败', 'error');
            }
        }

        // 测试退出登录
        function testLogoutFunction() {
            clearResults();
            addLog('测试退出登录功能...');
            
            const loginPage = document.getElementById('login-page');
            if (loginPage) {
                addLog('登录页面元素存在', 'success');
                addLog('注意：实际退出登录会切换到登录页面', 'warning');
                updateStatus('退出登录功能可用', 'success');
            } else {
                addLog('登录页面元素不存在', 'error');
                updateStatus('退出登录功能不可用', 'error');
            }
        }

        // 运行集成测试
        function runIntegrationTests() {
            clearResults();
            addLog('运行完整集成测试...');
            
            if (typeof UserGuideIntegrationTest !== 'undefined') {
                UserGuideIntegrationTest.runIntegrationTests().then(() => {
                    addLog('集成测试完成，请查看控制台详细结果', 'success');
                    updateStatus('集成测试完成', 'success');
                });
            } else {
                addLog('集成测试工具未加载', 'error');
                updateStatus('测试失败', 'error');
            }
        }

        // 启用调试模式
        function enableDebugMode() {
            clearResults();
            addLog('启用调试模式...');
            
            if (typeof UserGuideTest !== 'undefined') {
                UserGuideTest.enableDebugMode();
                addLog('调试模式已启用', 'success');
                updateStatus('调试模式已启用', 'success');
            } else {
                addLog('测试工具未加载', 'error');
                updateStatus('启用失败', 'error');
            }
        }

        // 快速修复
        function quickFix() {
            clearResults();
            addLog('执行快速修复...');
            
            if (typeof UserGuideIntegrationTest !== 'undefined') {
                UserGuideIntegrationTest.quickFix();
                addLog('快速修复完成', 'success');
                updateStatus('修复完成', 'success');
            } else {
                addLog('集成测试工具未加载', 'error');
                updateStatus('修复失败', 'error');
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('测试页面加载完成');
            addLog('准备测试用户引导系统集成...');
            
            // 检查是否在iframe中（主应用环境）
            if (window.parent !== window) {
                addLog('检测到iframe环境，可能影响某些功能', 'warning');
            }
            
            updateStatus('准备就绪', 'success');
        });
    </script>
</body>
</html>
