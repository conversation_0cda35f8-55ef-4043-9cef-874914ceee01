// 用户引导测试脚本
// 这个脚本用于测试用户引导功能

console.log('🧪 开始测试用户引导功能...');

// 等待页面完全加载
function waitForPageLoad() {
    return new Promise((resolve) => {
        if (document.readyState === 'complete') {
            resolve();
        } else {
            window.addEventListener('load', resolve);
        }
    });
}

// 等待UserGuide对象可用
function waitForUserGuide() {
    return new Promise((resolve, reject) => {
        let attempts = 0;
        const maxAttempts = 50; // 5秒超时
        
        const checkUserGuide = () => {
            attempts++;
            
            if (window.UserGuide && window.UserGuide.isInitialized) {
                console.log('✅ UserGuide 对象已找到并初始化');
                resolve(window.UserGuide);
            } else if (attempts >= maxAttempts) {
                reject(new Error('UserGuide 对象未找到或未初始化'));
            } else {
                setTimeout(checkUserGuide, 100);
            }
        };
        
        checkUserGuide();
    });
}

// 清除引导状态
function clearGuideState() {
    console.log('🔄 清除引导状态...');
    localStorage.removeItem('user_guide_completed');
    localStorage.removeItem('user_guide_version');
    localStorage.removeItem('user_guide_completed_completed_at');
}

// 测试用户引导
async function testUserGuide() {
    try {
        console.log('⏳ 等待页面加载...');
        await waitForPageLoad();
        
        console.log('⏳ 等待UserGuide对象...');
        const userGuide = await waitForUserGuide();
        
        console.log('📊 UserGuide状态:', {
            isInitialized: userGuide.isInitialized,
            isCompleted: userGuide.isCompleted(),
            shouldShowGuide: userGuide.shouldShowGuide(),
            stats: userGuide.getStats()
        });
        
        // 清除状态
        clearGuideState();
        
        // 重置引导
        console.log('🔄 重置用户引导...');
        userGuide.reset();
        
        // 启动引导
        console.log('🚀 启动用户引导...');
        userGuide.start();
        
        console.log('✅ 用户引导测试完成');
        
    } catch (error) {
        console.error('❌ 用户引导测试失败:', error);
        
        // 尝试备用方法
        console.log('🔄 尝试备用启动方法...');
        try {
            if (window.UserGuide) {
                window.UserGuide.restart();
                console.log('✅ 备用方法成功');
            }
        } catch (backupError) {
            console.error('❌ 备用方法也失败:', backupError);
        }
    }
}

// 立即执行测试
testUserGuide();

// 导出测试函数供手动调用
window.testUserGuide = testUserGuide;
window.clearGuideState = clearGuideState;

console.log('🔧 测试脚本已加载，可以手动调用 testUserGuide() 或 clearGuideState()');
