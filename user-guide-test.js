/**
 * 用户引导系统测试和调试工具
 * 
 * 提供测试功能、调试信息和开发工具
 * 仅在开发环境中使用
 */

class UserGuideTestSuite {
    constructor() {
        this.testResults = [];
        this.debugMode = false;
        this.debugPanel = null;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 开始运行用户引导测试套件...');
        
        this.testResults = [];
        
        // 基础功能测试
        await this.testBasicFunctionality();
        
        // 元素可见性测试
        await this.testElementVisibility();
        
        // 存储功能测试
        await this.testStorageFunctionality();
        
        // 可访问性测试
        await this.testAccessibility();
        
        // 响应式测试
        await this.testResponsiveness();
        
        // 输出测试结果
        this.outputTestResults();
        
        return this.testResults;
    }

    /**
     * 测试基础功能
     */
    async testBasicFunctionality() {
        const tests = [
            {
                name: '用户引导系统初始化',
                test: () => typeof UserGuide !== 'undefined' && UserGuide.isInitialized
            },
            {
                name: 'Driver.js库加载',
                test: () => typeof Driver !== 'undefined'
            },
            {
                name: '引导步骤配置',
                test: () => UserGuide.getGuideSteps().length > 0
            },
            {
                name: '可访问性增强器',
                test: () => typeof AccessibilityEnhancer !== 'undefined'
            },
            {
                name: '存储管理器',
                test: () => typeof GuideStorage !== 'undefined'
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, result ? '通过' : '失败');
            } catch (error) {
                this.addTestResult(test.name, false, `错误: ${error.message}`);
            }
        }
    }

    /**
     * 测试元素可见性
     */
    async testElementVisibility() {
        const requiredElements = [
            '.sidebar',
            '#ai-assistant-menu',
            '.central-input-container',
            '.notification-container',
            '.user-profile'
        ];

        for (const selector of requiredElements) {
            const isVisible = UserGuide.isElementVisible(selector);
            this.addTestResult(
                `元素可见性: ${selector}`,
                isVisible,
                isVisible ? '元素可见' : '元素不可见或不存在'
            );
        }
    }

    /**
     * 测试存储功能
     */
    async testStorageFunctionality() {
        const testKey = 'test_guide_storage';
        const testValue = { test: true, timestamp: Date.now() };

        try {
            // 测试存储
            const setResult = GuideStorage.setItem(testKey, testValue);
            this.addTestResult('存储设置', setResult, setResult ? '成功' : '失败');

            // 测试读取
            const getValue = GuideStorage.getItem(testKey);
            const getResult = getValue && getValue.test === true;
            this.addTestResult('存储读取', getResult, getResult ? '成功' : '失败');

            // 测试删除
            const removeResult = GuideStorage.removeItem(testKey);
            this.addTestResult('存储删除', removeResult, removeResult ? '成功' : '失败');

            // 测试清空后读取
            const emptyValue = GuideStorage.getItem(testKey);
            const emptyResult = emptyValue === null;
            this.addTestResult('存储清空验证', emptyResult, emptyResult ? '成功' : '失败');

        } catch (error) {
            this.addTestResult('存储功能', false, `错误: ${error.message}`);
        }
    }

    /**
     * 测试可访问性
     */
    async testAccessibility() {
        const tests = [
            {
                name: '屏幕阅读器公告区域',
                test: () => !!document.getElementById('user-guide-announcements')
            },
            {
                name: '键盘导航支持',
                test: () => AccessibilityEnhancer.keyboardHandlers.size > 0
            },
            {
                name: '可访问性偏好检测',
                test: () => {
                    const prefs = AccessibilityEnhancer.checkAccessibilityPreferences();
                    return typeof prefs.reducedMotion === 'boolean';
                }
            }
        ];

        for (const test of tests) {
            try {
                const result = await test.test();
                this.addTestResult(test.name, result, result ? '支持' : '不支持');
            } catch (error) {
                this.addTestResult(test.name, false, `错误: ${error.message}`);
            }
        }
    }

    /**
     * 测试响应式设计
     */
    async testResponsiveness() {
        const viewports = [
            { width: 1920, height: 1080, name: '桌面大屏' },
            { width: 1366, height: 768, name: '桌面标准' },
            { width: 768, height: 1024, name: '平板' },
            { width: 375, height: 667, name: '手机' }
        ];

        // 注意：这个测试只能检查CSS媒体查询，不能实际改变窗口大小
        for (const viewport of viewports) {
            const mediaQuery = window.matchMedia(`(max-width: ${viewport.width}px)`);
            this.addTestResult(
                `响应式支持: ${viewport.name}`,
                true,
                `媒体查询: ${mediaQuery.matches ? '匹配' : '不匹配'}`
            );
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(name, passed, details) {
        this.testResults.push({
            name,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 输出测试结果
     */
    outputTestResults() {
        const passed = this.testResults.filter(r => r.passed).length;
        const total = this.testResults.length;
        
        console.log(`\n📊 测试结果: ${passed}/${total} 通过`);
        console.log('=' .repeat(50));
        
        this.testResults.forEach(result => {
            const icon = result.passed ? '✅' : '❌';
            console.log(`${icon} ${result.name}: ${result.details}`);
        });
        
        console.log('=' .repeat(50));
        
        if (passed === total) {
            console.log('🎉 所有测试通过！');
        } else {
            console.log(`⚠️  ${total - passed} 个测试失败，请检查相关功能`);
        }
    }

    /**
     * 启用调试模式
     */
    enableDebugMode() {
        this.debugMode = true;
        this.createDebugPanel();
        console.log('🔧 调试模式已启用');
    }

    /**
     * 禁用调试模式
     */
    disableDebugMode() {
        this.debugMode = false;
        if (this.debugPanel) {
            this.debugPanel.remove();
            this.debugPanel = null;
        }
        console.log('🔧 调试模式已禁用');
    }

    /**
     * 创建调试面板
     */
    createDebugPanel() {
        if (this.debugPanel) return;

        this.debugPanel = document.createElement('div');
        this.debugPanel.className = 'user-guide-debug';
        this.debugPanel.innerHTML = `
            <h4>🔧 引导调试面板</h4>
            <ul>
                <li>状态: ${UserGuide.isCompleted() ? '已完成' : '未完成'}</li>
                <li>版本: ${UserGuide.options.currentVersion}</li>
                <li>步骤数: ${UserGuide.getGuideSteps().length}</li>
                <li>当前步骤: ${UserGuide.currentStep}</li>
            </ul>
            <div style="margin-top: 8px;">
                <button onclick="UserGuideTest.startGuide()" style="margin-right: 4px; padding: 4px 8px; font-size: 10px;">开始</button>
                <button onclick="UserGuideTest.resetGuide()" style="margin-right: 4px; padding: 4px 8px; font-size: 10px;">重置</button>
                <button onclick="UserGuideTest.runTests()" style="padding: 4px 8px; font-size: 10px;">测试</button>
            </div>
        `;

        document.body.appendChild(this.debugPanel);
    }

    /**
     * 更新调试面板
     */
    updateDebugPanel() {
        if (!this.debugPanel) return;

        const statusList = this.debugPanel.querySelector('ul');
        if (statusList) {
            statusList.innerHTML = `
                <li>状态: ${UserGuide.isCompleted() ? '已完成' : '未完成'}</li>
                <li>版本: ${UserGuide.options.currentVersion}</li>
                <li>步骤数: ${UserGuide.getGuideSteps().length}</li>
                <li>当前步骤: ${UserGuide.currentStep}</li>
                <li>可见元素: ${this.countVisibleElements()}</li>
            `;
        }
    }

    /**
     * 计算可见元素数量
     */
    countVisibleElements() {
        const selectors = ['.sidebar', '#ai-assistant-menu', '.central-input-container', '.notification-container', '.user-profile'];
        return selectors.filter(selector => UserGuide.isElementVisible(selector)).length;
    }

    /**
     * 便捷方法：开始引导
     */
    startGuide() {
        UserGuide.start();
        this.updateDebugPanel();
    }

    /**
     * 便捷方法：重置引导
     */
    resetGuide() {
        UserGuide.reset();
        this.updateDebugPanel();
        console.log('🔄 引导状态已重置');
    }

    /**
     * 便捷方法：运行测试
     */
    async runTests() {
        await this.runAllTests();
        this.updateDebugPanel();
    }

    /**
     * 模拟不同的用户场景
     */
    simulateUserScenarios() {
        const scenarios = [
            {
                name: '新用户首次访问',
                action: () => {
                    UserGuide.reset();
                    setTimeout(() => UserGuide.start(), 1000);
                }
            },
            {
                name: '返回用户',
                action: () => {
                    UserGuide.markAsCompleted();
                    console.log('模拟返回用户（引导已完成）');
                }
            },
            {
                name: '手动重启引导',
                action: () => {
                    UserGuide.restart();
                }
            }
        ];

        console.log('🎭 可用的用户场景模拟:');
        scenarios.forEach((scenario, index) => {
            console.log(`${index + 1}. ${scenario.name}`);
        });

        // 将场景添加到全局对象，方便在控制台中调用
        window.UserGuideScenarios = scenarios;
        console.log('💡 使用 UserGuideScenarios[index].action() 来模拟场景');
    }
}

// 创建全局测试实例
window.UserGuideTest = new UserGuideTestSuite();

// 在开发环境中自动启用调试模式
if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
    console.log('🔧 检测到开发环境，启用引导调试功能');
    console.log('💡 使用以下命令进行测试:');
    console.log('   UserGuideTest.runAllTests() - 运行所有测试');
    console.log('   UserGuideTest.enableDebugMode() - 启用调试面板');
    console.log('   UserGuideTest.simulateUserScenarios() - 查看用户场景模拟');
}

// 导出测试套件
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserGuideTestSuite;
}
