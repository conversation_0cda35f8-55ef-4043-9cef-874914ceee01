# 用户引导系统使用说明

## 📋 概述

本项目集成了一个完整的用户引导系统，基于Driver.js库构建，为新用户提供交互式的功能介绍和操作指导。

## ✨ 功能特性

### 🎯 核心功能
- **多步骤引导流程** - 7个精心设计的引导步骤
- **智能触发机制** - 首次访问用户自动显示引导
- **持久化存储** - 记录用户引导完成状态，避免重复显示
- **手动重启** - 用户可随时重新查看引导

### 🎨 用户体验
- **响应式设计** - 适配桌面、平板、手机等不同设备
- **主题集成** - 完美融入项目的CSS变量系统和多主题支持
- **流畅动画** - 平滑的过渡效果和高亮动画
- **直观导航** - 清晰的步骤指示和导航按钮

### ♿ 可访问性支持
- **键盘导航** - 支持方向键、ESC、Tab等键盘操作
- **屏幕阅读器** - 完整的ARIA标签和语音公告支持
- **焦点管理** - 智能的焦点跳转和恢复
- **高对比度** - 支持高对比度模式和减少动画偏好

## 🚀 快速开始

### 1. 文件结构
```
├── user-guide.js          # 核心引导逻辑
├── user-guide.css         # 引导样式文件
├── user-guide-test.js     # 测试和调试工具（开发环境）
└── USER_GUIDE_README.md   # 使用说明文档
```

### 2. 基本使用

引导系统会在页面加载完成后自动初始化：

```javascript
// 自动初始化（已集成在script.js中）
UserGuide.init().then(() => {
    console.log('引导系统初始化完成');
});
```

### 3. 手动控制

```javascript
// 开始引导
UserGuide.start();

// 重新开始引导
UserGuide.restart();

// 跳过引导
UserGuide.skip();

// 重置引导状态
UserGuide.reset();

// 检查是否已完成
UserGuide.isCompleted();
```

## 📖 引导步骤说明

| 步骤 | 目标元素 | 说明内容 |
|------|----------|----------|
| 1 | 整个页面 | 欢迎消息和引导概述 |
| 2 | 侧边栏 | 主导航菜单介绍 |
| 3 | AI助手菜单 | 核心功能详细说明 |
| 4 | 中央输入框 | 操作入口和快速体验 |
| 5 | 通知中心 | 消息管理功能 |
| 6 | 用户资料 | 个人设置和重启引导 |
| 7 | 整个页面 | 完成祝贺和后续指导 |

## ⚙️ 配置选项

### 基本配置
```javascript
const options = {
    storageKey: 'user_guide_completed',    // 存储键名
    versionKey: 'user_guide_version',      // 版本键名
    currentVersion: '1.0.0',               // 当前版本
    autoStart: true,                       // 自动开始
    showProgress: true,                    // 显示进度
    allowClose: true,                      // 允许关闭
    overlayOpacity: 0.75,                  // 遮罩透明度
    smoothScroll: true                     // 平滑滚动
};
```

### 事件回调
```javascript
UserGuide.options = {
    ...UserGuide.options,
    onStart: () => console.log('引导开始'),
    onComplete: () => console.log('引导完成'),
    onSkip: () => console.log('引导跳过'),
    onStepStart: (data) => console.log('步骤开始', data),
    onStepEnd: (data) => console.log('步骤结束', data)
};
```

## 🎨 样式自定义

### CSS变量
引导系统使用项目的CSS变量系统，支持主题切换：

```css
.user-guide-driver {
    --guide-primary-color: var(--primary-color);
    --guide-background: var(--surface-color);
    --guide-text-color: var(--text-color);
    --guide-border-color: var(--border-color);
    /* 更多变量... */
}
```

### 自定义步骤样式
```css
/* 欢迎步骤 */
.guide-step-welcome .driver-popover {
    max-width: 400px !important;
    text-align: center !important;
}

/* AI助手步骤 */
.guide-step-ai .driver-popover-title {
    color: var(--guide-primary-color) !important;
}
```

## 🧪 测试和调试

### 开发环境工具
在开发环境中，系统会自动加载测试工具：

```javascript
// 运行所有测试
UserGuideTest.runAllTests();

// 启用调试面板
UserGuideTest.enableDebugMode();

// 模拟用户场景
UserGuideTest.simulateUserScenarios();
```

### 测试覆盖
- ✅ 基础功能测试
- ✅ 元素可见性测试
- ✅ 存储功能测试
- ✅ 可访问性测试
- ✅ 响应式设计测试

## 🔧 故障排除

### 常见问题

**Q: 引导不显示？**
A: 检查以下几点：
1. Driver.js库是否正确加载
2. 目标元素是否存在且可见
3. 用户是否已完成过引导（检查localStorage）

**Q: 样式显示异常？**
A: 确保：
1. user-guide.css文件正确加载
2. CSS变量系统正常工作
3. 没有样式冲突

**Q: 键盘导航不工作？**
A: 验证：
1. AccessibilityEnhancer是否正确初始化
2. 页面焦点是否在引导区域内
3. 浏览器是否支持相关API

### 调试命令
```javascript
// 检查系统状态
console.log('引导状态:', UserGuide.getStats());

// 检查兼容性
console.log('浏览器兼容性:', UserGuide.checkCompatibility());

// 检查可访问性
console.log('可访问性偏好:', AccessibilityEnhancer.checkAccessibilityPreferences());
```

## 📱 响应式支持

### 断点适配
- **桌面** (>1024px): 完整功能和样式
- **平板** (768px-1024px): 调整弹出框大小和位置
- **手机** (<768px): 优化触摸操作和紧凑布局

### 移动端优化
- 触摸友好的按钮大小
- 防止内容溢出视口
- 优化文字大小和行距

## 🌐 浏览器支持

### 支持的浏览器
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 13+
- ✅ Edge 80+

### 必需的API
- localStorage
- CustomEvent
- MutationObserver
- getBoundingClientRect

## 📈 性能优化

### 加载优化
- 延迟加载Driver.js库
- 按需初始化组件
- 避免阻塞页面渲染

### 内存管理
- 自动清理事件监听器
- 销毁不需要的DOM元素
- 防止内存泄漏

## 🔄 版本更新

当引导内容更新时，修改版本号即可：

```javascript
UserGuide.options.currentVersion = '1.1.0';
```

系统会自动检测版本变化，为用户重新显示引导。

## 📞 技术支持

如有问题或建议，请：
1. 查看浏览器控制台错误信息
2. 运行测试套件检查系统状态
3. 参考本文档的故障排除部分

---

**最后更新**: 2024年6月18日  
**版本**: 1.0.0  
**作者**: 跨境运营助手团队
