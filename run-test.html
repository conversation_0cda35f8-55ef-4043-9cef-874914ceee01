<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运行用户引导测试</title>
</head>
<body>
    <h1>用户引导测试</h1>
    <p>点击下面的按钮来测试用户引导功能：</p>
    <button onclick="runTest()">运行测试</button>
    
    <script>
        function runTest() {
            // 动态加载测试脚本
            const script = document.createElement('script');
            script.src = 'test-user-guide.js';
            document.head.appendChild(script);
        }
        
        // 自动运行测试
        setTimeout(runTest, 1000);
    </script>
</body>
</html>
