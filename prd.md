## 1. 引言

### 1.1 项目概述

跨境运营助手是一款旨在帮助跨境电商运营人员和品牌营销人员简化网红（或称创作者/KOL）建联流程的Web应用程序。它利用AI技术分析产品特性，智能匹配合适的海外创作者（特别是YouTube博主），管理建联过程，并追踪合作效果，从而提高网红营销的效率和成功率。

### 1.2 产品路径：

**产品目标**：推广商品》推广品牌》接管商家推广全流程

**产品路径**：自动化匹配》自动化建联》Ai化建联》》企业Ai投放Agent

**商业化**：卖网红信息》卖 Ai建联服务订阅》抽推广服务佣金

### 1.3 项目目标

- **提高效率：** 自动化产品分析、创作者匹配和初步沟通邮件生成，减少人工搜索和筛选时间。
- **提升匹配精准度：** 利用AI分析产品与创作者内容、受众的契合度，推荐更合适的合作人选。
- **简化管理：** 提供集中的平台来管理产品信息、创作者资料、建联进度和沟通记录。
- **数据驱动决策：** 通过仪表盘和（未来规划的）数据分析功能，帮助用户了解合作效果，优化策略。

---

### 2. 功能需求

### 2.1 用户认证与授权

- **2.1.1 登录:**
    - 目前只提供谷歌账户的登录方式：
        - **Google账号登录:**
            - 首次进入时，使用任何功能和按钮，都跳转到谷歌登录界面，点击【使用 google 账号登录】后跳转至Google账号选择页面。
            - 接入谷歌的 **OAuth 2.0 api**，获取用户授权及必要的 `access_token` 和 `refresh_token`，同下方邮箱账号绑定。
            - 权限需要用户的邮箱管理权限，获取读取邮箱，存储用户的头像和谷歌名称，系统中用此头像和名称进行显示。
            - 安全存储传输用户凭证和Token。
- **2.1.2 多邮箱账户管理:**
    - **邮箱配置入口：**
        - 在账户设置页面提供"邮箱配置"功能入口
        - 支持添加和管理多种类型的邮箱账号
    - **邮箱类型支持：**
        - **QQ邮箱：** 自动配置SMTP/IMAP参数，用户只需填写邮箱地址和授权码
        - **163邮箱：** 自动配置SMTP/IMAP参数，用户只需填写邮箱地址和授权码
        - **其他邮箱：** 用户手动配置所有SMTP/IMAP参数
        - **Gmail：** 使用OAuth 2.0授权模式，支持添加多个Gmail账号
    - **配置指导功能：**
        - QQ邮箱和163邮箱显示配置指导卡片，提供获取授权码的跳转链接
        - 提供连接测试功能验证配置正确性
    - **邮箱管理：**
        - 支持设置主要发件邮箱
        - 支持启用/禁用特定邮箱
        - 支持删除不需要的邮箱配置
- **2.1.3 登出:**
    - 用户可通过账户设置页面或用户菜单头像旁的设置按钮登出。
    - 登出后清除用户会话/Token，返回主界面。

### 2.2 核心界面 (UI)

- **2.2.1 整体布局:**
    - 采用左侧固定侧边栏 + 右侧主内容区的布局。
    - 主内容区顶部包含固定导航栏（Header）。
- **2.2.2 侧边栏 (Sidebar):**
    - **Logo区域:** 显示应用Logo和名称“跨境运营助手”。
    - **主菜单:**
        - **仪表盘 (Dashboard):** 默认首页，显示核心指标概览。
        - **产品库 (Product Library):** 管理产品信息。显示待处理项角标（如有）。
        - **AI助手 (AI Assistant):** 核心功能，进行产品分析和创作者匹配。
        - **建联记录 (Outreach Records):** 管理网红合作流程。显示待处理项角标（如有）。
        - 菜单项应高亮显示当前活动页面。
    - **用户信息区域:**
        - 显示当前登录用户的头像、用户名、邮箱。
        - 包含一个设置图标，点击可唤出菜单，包括登出和打开账户设置页面。
- **2.2.3 顶部导航栏 (Header):**
    - **页面标题:** 动态显示当前所在模块的名称（如 "AI助手", "产品库"）。
    - **~~全局搜索框:** 允许用户在应用内进行搜索。~~
    - **用户操作区:**
        - 通知图标：点击可展开通知列表
            - 此处展示最新的达人回信，点击跳转到对应的达人窗口
            - 查看全部通知，则链接到近期活动-查看全部中
            - 有角标数量提示数量
        - 用户头像：点击可展开用户菜单，同左下角设置图标。

### 2.3 仪表盘 (Dashboard)

- **2.3.1 KPI概览卡片:**
    - **活跃合作 (Active Collaborations):** 定义为“状态为‘沟通中’、‘已确认’或‘推广中’的建联记录数量”。
    - **本周新增建联 (New Outreach This Week):** 统计本周（周一至周日）内新创建的建联记录数量。
    - **活动触达潜力 (Potential Reach):** 实时计算所有状态为“推广中”的建联记录关联的创作者的订阅者数量总和。
    - **平均回复率 (Average Reply Rate):** 计算（状态为“沟通中”及后续状态的建联数 / 状态为“已联系”及后续状态的建联数） * 100%。
    - **趋势百分比:** 每个KPI卡片显示与上周同期数据的对比百分比（正数绿色向上箭头，负数红色向下箭头）。
    - **[后台需求]** 需要定时或实时计算这些指标，看性能是否能满足进入时刷新。
- **2.3.2 近期活动 (Recent Activities):**
    - 以时间线形式展示最近的应用内重要事件。
    - **事件类型:**
        - 邮件回复（需邮件集成）：如“Two Minute Papers 回复了你的邮件”。
        - 状态更新：如“MattVidPro AI 的合作状态更新为「已确认」”。
        - AI通知：如“AI助手推荐了3位新的合适博主”。
        - 操作记录：如“你向 AI TV 发送了合作邮件”。
    - 显示事件描述和发生时间（如“42分钟前”，“昨天 14:28”）。
    - 提供“查看全部”按钮，链接到更详细的活动日志页面（待设计）。
    - **[后台需求]** 记录用户操作和系统事件，并能根据用户过滤。
- **2.3.3 通知中心：**
    - 存在一个通知中心，通过右上角的通知图标，以及主页的最近活动进入
    - 用户查看过去的操作log
    - 通知中心有两个部分，一个用户主动操作的 log 记录时间线，一个是达人回复邮件的意图展示
- **2.3.4 合作漏斗 (Collaboration Funnel):**
    - 可视化展示建联流程中各个阶段的数量。
    - **阶段定义:**
        - **已联系 (Contacted):** 已通过平台发送首次建联邮件/消息的创作者数量。
        - **沟通中 (Communicating):** 收到创作者回复的建联数量。
        - **已确认 (Confirmed):** 用户手动标记为已达成合作意向的建联数量
        - **推广中 (Promoting):** 用户手动标记或系统（基于链接/邮件内容）识别为正在进行推广活动的建联数量。
    - 提供时间范围筛选器（如：本月、上月、本季度、全年）。
    - 显示每个阶段的数量和相对于上一阶段的转化率（可选）。
    - 显示总转化率（推广中 / 已联系）和平均合作周期（从已联系到推广中的平均天数）。
    - **[后台需求]** 基于建联记录的状态和时间戳进行统计。

### 2.4 产品库 (Product Library)

- **2.4.1 产品列表视图:**
    - 默认以网格（卡片）形式展示产品。
- **2.4.2 产品卡片:**
    - 显示产品图片。
    - 显示产品名称。
    - 显示产品分类标签（如：电子产品、价格亚马逊货币显示）。
    - 显示产品核心描述（限制ai 总结行数）。
    - 提供操作按钮：编辑、更多（包含删除等）。
- **2.4.3 添加/编辑产品:**
    - 提供“添加产品”按钮，等同于跳转到 ai 助手界面
- **2.4.4 搜索与筛选:**
    - 提供搜索框，支持模糊搜索产品名称、类别、描述关键词搜索。
    - 提供筛选控件（当前显示为标签和下拉按钮），支持按分类、价格区间等进行筛选。
    - 显示当前筛选条件和结果数量。
- **2.4.5 排序:**
    - 提供排序下拉菜单，支持按最新添加、价格（低到高/高到低）、受欢迎程度（待定义）排序。

### 2.5 AI助手 (AI Assistant)

- **2.5.1 界面布局:**
    - 初始页面：类似ai 助手页面放入商品链接
    - **AI助手信息区:** 显示AI助手头像、名称、当前关联产品。
    - **聊天容器:** 显示用户与AI的对话消息流。
    - **输入区域:** 文本输入框、附加操作按钮（链接、附件）、发送按钮。
- **2.5.2 核心交互流程:**
    - 一个产品对应一个 ai 对话，类似 chatgpt侧边可以切换商品的对应ai，包装感受上是每个商品都有对应专属 ai。
        - 对话过程并不是真的跟 ai对话，有固定的流程，模拟 ai 对话/先做成固定流程的体验
    
    **输入产品信息:**
    
    - 用户可在输入框粘贴产品URL或输入产品描述。
        - **链接识别:** 系统自动识别输入的URL类型：
            - **亚马逊链接:** 直接调用现有的亚马逊页面爬虫算法
            - **非亚马逊链接:** 调用通用爬虫接口获取页面数据
    - 点击发送按钮或按Enter键提交。
    
    **AI分析产品:**
    
    - **亚马逊商品分析流程:**
        - AI显示正在分析的消息。
        - 展示分析步骤（抓取链接、提取特征、匹配算法）。
        - 展示分析结果卡片，包含可编辑的产品名称、价格、核心特性、目标受众。
        - 用户可选择修改信息或直接确认开始匹配。
    - **非亚马逊商品分析流程:**
        - AI显示正在分析的消息。
        - **数据处理步骤:**
            - 调用通用爬虫接口获取页面原始数据
            - 使用数据清洗脚本处理爬虫返回的数据
            - 将清洗后的数据提交给LLM算法提取商品信息
        - **信息完整性检查:**
            - 系统检查提取的商品信息是否包含以下必需字段：
                - 商品名称
                - 商品价格
                - 商品特点
                - 商品类别
                - 详细描述
                - 品牌名称
        - **完整信息处理:**
            - 如果信息完整，直接展示分析结果卡片，流程同亚马逊商品
        - **不完整信息处理:**
            - 如果信息不完整，展示**信息补全卡片**：
                - 显示已提取的信息字段
                - 高亮标示缺失的必需字段
                - 提供可编辑的输入框供用户补全信息
                - 显示"完成补全"按钮
            - 用户补全信息后点击"完成补全"：
                - 将完整信息重新提交给算法进行商品标签提取
                - 展示最终的分析结果卡片
        - 用户可选择修改信息或直接确认开始匹配。
    - **AI匹配创作者:**
        - AI显示正在匹配的消息。
        - 匹配完成后，显示匹配到的创作者列表。
    - **营销活动设置:**
        - **触发时机:** 用户确认商品分析结果后，系统自动展示营销设置卡片
        - **设置界面:**
            - 展示**营销活动设置卡片**，包含两个必填项：
                - **营销总预算 (单位美金):**
                    - 输入框，支持数字输入。默认 0
                    - 建议范围提示：1$ - 100,000$
                - **预期建联数量:**
                    - 输入框，进行数据校验，只可填写 5 到 50 个
                    - 可选范围：5-50个，默认自动填写 10 个
                    - 说明文字："系统将为您匹配相应数量的优质达人"
                - **后台处理逻辑:**
                    - **数据获取规则:** 根据用户填写的数量X，系统从达人数据库获取 X * 2倍数量的达人信息 给到匹配算法
                    - **数据传递:** 将候选池中的所有达人信息传递给商品与达人匹配算法
                    - **最终输出:** 匹配算法分析候选池后，按匹配度排序并严格返回用户设置的X个达人
        - **用户交互:**
            - 用户填写完成后点击"开始匹配达人"按钮
            - 系统验证输入有效性（预算 > 0，建联数量在5-50 个）
            - 验证通过后进入"AI匹配创作者"步骤
        - **数据存储:**
            - 营销总预算存储到商品信息表的 `marketing_budget` 字段
            - 预期建联数量传递给匹配算法，控制返回的达人数量
    - **展示推荐创作者:**
        - 以卡片列表形式展示推荐的创作者。
        - **创作者卡片内容:** 头像、名称（含认证标识）、核心数据（订阅数、观看数、视频数）、相关度评分、详细匹配理由（基于产品特性和创作者内容）、AI沟通建议、操作按钮（添加到建联记录、查看详情）。
        - **[后台需求]** 需要创作者数据库（包含频道信息、内容标签、历史合作、受众画像等）。实现匹配算法，计算相关度并生成推荐理由和建议。数据来源可能是爬虫、第三方API或合作伙伴。
        - 提供JSON视图，展示更详细的后台数据（联系方式、预估成本等）。
    - **生成建联邮件 :**
        - （在原型后续流程或直接在聊天中）用户选择一个或多个推荐的创作者。
        - 用户请求生成建联邮件。
        - AI显示正在生成的消息。
        - 展示邮件预览卡片，包含收件人（选中的创作者）、可编辑的主题、AI生成的个性化邮件正文。
        - **[后台需求]** 实现基于产品信息、创作者特点和预设模板的个性化邮件生成（可用GPT等大模型）。
    - **发送邮件:**
        - 用户点击发送。
        - AI显示发送中及发送成功消息。
        - 提示用户可在“建联记录”中跟进。
        - **[后台需求]** 调用邮件API（如Gmail API）发送邮件，并将记录添加到建联记录。
- **2.5.3 第一次建联后:**
    
    当用户为某个商品发送了第一批建联邮件后，该商品的AI对话窗口将转变为以下管理模式：
    
    **界面核心元素:**
    
    - **当前活动商品:** 始终清晰标示当前对话是围绕哪个商品进行的。
    - **已联系博主列表:** 显示本次活动已发送邮件的博主卡片。
    - **核心功能按钮:** 固定的几个功能按钮。
    - **AI 消息推送区域:** AI 主动发送提醒和分析结果的地方。
    
    **具体功能流程:**
    
    **功能一：查找更多相似博主**
    
    - **触发:** 用户在AI对话界面点击固定按钮 `查找更多博主`。
    - **AI响应:**
        1. AI显示：“正在根据 [商品名称] 的特性为您寻找更多合适的YouTube博主...”
        2. AI重新运行匹配算法（可能排除已联系过的博主）。
        3. AI展示新一批推荐博主的卡片列表，信息同首次匹配（头像、名称、粉丝数、匹配度、匹配理由等）。
    - **用户交互:**
        1. 用户在新列表中勾选想要联系的博主。
        2. 点击 `生成建联邮件` 按钮。
        3. 流程回到之前的“邮件生成与预览”步骤 (原型图5)，之后发送邮件 (原型图6)。
        4. 新联系的博主自动加入到“已联系博主列表”中。
    - **价值:** 无需重复输入商品信息，快速扩大潜在合作博主范围。
    
    **功能二：建联状态追踪与接管提醒**
    
    - **状态展示与手动更新:**
        1. AI对话界面清晰展示“已联系博主列表”，每位博主卡片上显示**当前状态**（默认为“已发送”）。
        2. 每个博主卡片旁提供**状态更新按钮组**，例如：
            - `标记为已回复`
            - `标记为无意向`
            - `标记为洽谈中`
            - `标记为已合作`
        3. **用户操作:** 用户在自己的邮箱或其他渠道得知博主回复后，回到此界面，点击对应博主的按钮，手动更新状态。AI记录该状态。
    - **邮件回复功能:**
        1. **触发:** 当达人回复邮件时，ai 意图显示到
        2. **AI响应:** 立刻在消息区域推送一条**高亮提醒**：
        
        > 🔔 跟进提醒： 检测到 [博主名称] 已回复！建议您立即前往“达人建联页”进行沟通管理，把握合作机会。
        > 
        1. **交互:** 该提醒下方提供一个明确的按钮：
            - `前往接管建联 ([博主名称])`
    - **跳转至建联页:**
        1. 用户点击 `前往接管建联` 按钮。
        2. 系统**自动跳转**到建联记录模块
        3. 该页面应**自动聚焦**到刚刚标记为已回复的这位 **[博主名称]** 以及关联的 **[商品名称]** 的沟通记录上，方便用户查看历史邮件、记录沟通要点、安排下一步行动等。
    - **价值:** 将AI助手的“发现与触达”功能与实际的“沟通与管理”流程无缝衔接。虽然状态更新依赖手动，但AI的提醒和一键跳转极大提升了响应效率，解决了用户“知道回复了，然后去哪里处理”的问题。
    
    **功能三：建联效果分析**
    
    - **触发:** 用户点击AI对话界面的固定按钮 `查看效果分析`。
    - **AI响应:** AI基于当前用户为该商品**手动标记的所有博主状态**，实时计算并展示分析结果：
    
    > [商品名称] - 建联效果分析 (截至 [当前时间])已联系博主总数: [N] 位已回复博主数: [M] 位 (状态为“已回复”、“洽谈中”、“已合作”的总和)当前回复率: [M/N * 100]%初步意向率: [P] 位 (状态为“洽谈中”、“已合作”的总和) / [N] 位 * 100%无意向反馈: [Q] 位 (状态为“无意向”的数量)
    > 
    - **用户交互:** 结果以文本形式清晰展示在对话界面中。下方可提供按钮 `关闭分析` 或让其自然留在对话流中。
    - **价值:** 为用户提供针对单一商品营销活动的快速、量化的效果反馈，帮助判断策略是否有效，是否需要调整（例如，是否要“查找更多博主”或优化邮件模板）。

### 2.6 建联记录 (Outreach Records)

- **2.6 建联记录 (Outreach Records)**
    - **2.6.1 列表视图:**
        - 列定义: 创作者（头像、名称、核心数据）、关联产品（名称、分类）、当前状态、日期（上次联系、创建日期）、操作
        - 状态标签: 使用不同颜色和图标清晰标识建联状态（沟通中、已确认等）
    - **2.6.2 筛选与搜索:**
        - 提供状态标签页（全部、沟通中、已确认、推广中、已结束）进行快速筛选
        - 提供搜索框，支持按创作者名称、产品、状态关键词搜索
        - 提供按产品筛选的功能
    - **2.6.3 建联详情页面:**
        - 点击列表项（非按钮区域）时，跳转到独立的详情页面
        - **顶部达人信息区域:**
            - **左侧达人卡片:**
                - 显示达人头像、名称（含认证标识）
                - 关键数据（订阅数、总观看数）
                - 达人标签：显示达人的内容标签（如：科技达人、AI专家、产品测评）
                - **综合人设/风格 (overallPersonaAndStyle):** AI生成的达人整体形象和风格描述（如"专业科技测评师"、"轻松幽默生活博主"）
                - **主要受众画像 (mainAudience):** AI生成的核心受众特征描述（如"以美国年轻男性为主（18-34岁）"）
                - **潜在合作品牌类型 (potentialBrandType):** AI生成的适合合作的品牌品类列表（如["数码产品", "快时尚", "生活用品"]）
                - **达人总体评价 (influencerEval):** AI生成的综合评估，包含内容质量、互动表现、独特性和发展潜力等维度
            - **右侧产品信息:**
                - 显示关联的产品图片、名称
                - 简要描述、价格区间
        - **中部状态与操作栏:**
            - 时间信息: 显示上次发布时间（如"24h"）、上次沟通时间（如"2天前"）
            - 外部链接: "访问频道"按钮，直接跳转达人主页
            - 状态管理: 当前状态显示（如"沟通中"）带下拉菜单可修改状态
        - **内容标签页:**
            - 沟通记录标签页: 默认选中，显示所有沟通时间线
            - 近期内容标签页: 展示该创作者最近在各平台发布的内容
    - **2.6.4 沟通时间线 (沟通记录标签页):**
        - **基本布局:**
            - 按时间倒序排列，清晰的时间线布局
            - 左侧图标区分不同类型的记录
        - **邮件记录类型:**
            - **邮件回复（自动同步）:**
                - 显示达人主动回复的邮件
                - 自动从邮箱同步获取
            - **合作意向邮件（自动同步）:**
                - 显示系统发送的建联邮件
                - 记录发送状态和时间
            - **手动记录:**
                - 用户手动添加的沟通记录
                - 支持多种记录类型（邮件、通话、聊天、备注）
        - **邮件条目内容:**
            - 邮件类型标题（如"邮件回复（自动同步）"）
            - **邮件元信息:**
                - 发件人邮箱和时间戳（如"来自: <EMAIL> • 2023/03/21 14:30"）
            - **翻译功能:**
                - 蓝色翻译按钮
                - 点击后在邮件原文下方显示中文翻译
                - **[后台需求]** 需要集成翻译算法
            - **操作按钮:**
                - "查看邮件原文"按钮 - 查看完整邮件内容
                - "回复"按钮 - 快速回复该邮件
    - **2.6.6 发送邮件功能:**
        - **触发方式:**
            - 在沟通记录页面底部直接发送新邮件
            - 通过"回复"按钮回复特定邮件
        - **发送邮件界面:**
            - **收件人字段:**
                - 自动填充达人邮箱地址
                - 支持手动编辑（如需要抄送）
            - **邮件主题:**
                - 可编辑输入框
                - 回复时自动添加"Re:"前缀
                - 支持占位符提示
            - **邮件内容:**
                - 大文本输入框，支持富文本编辑
                - 支持插入链接、格式化文本
                - 回复时可引用原邮件内容
            - **发送按钮:**
                - 完成邮件发送
                - 自动记录到沟通时间线
                - **[后台需求]** 需要集成Gmail API或SMTP服务发送邮件
        - **邮件发送后处理:**
            - 自动在时间线中添加"已发送邮件"记录
            - 更新"上次联系时间"
            - 如果是首次发送，状态自动更改为"已联系"
    - 展示创作者在YouTube、Instagram等平台的近期帖子
    - 显示内容：缩略图、标题、发布信息、互动数据
    - 提供"加载更多"功能
    - **[后台需求]** 需要集成YouTube Data API、Instagram Graph API等
    - **2.6.6 状态管理:**
        - **状态流转规则:**
            - 未联系 → 已联系（发送首次邮件）
            - 已联系 → 沟通中（收到回复）
            - 沟通中 → 已确认（达成合作意向）
            - 已确认 → 推广中（开始推广活动）
            - 推广中 → 已完成（推广活动结束）
            - 任何状态 → 已拒绝（达人拒绝合作）
        - **状态修改:**
            - 用户可在详情页通过下拉菜单手动修改状态
            - 系统根据邮件内容智能建议状态更新
            - 状态变更自动记录到时间线
    - **2.6.7 AI邮件意图识别功能:**
        - **功能概述:** 对网红回复邮件进行智能分析，提取合作意图和项目进展信息
        - **核心概念:**
            - **Scene（邮件场景）:** 当前邮件表达的主要合作意图，标记在具体邮件记录上
            - **Stage（项目阶段）:** 整个建联项目当前所处的标准流程节点，记录在建联记录上
            - **Summary（邮件摘要）:** 30-50字自然语言概括邮件核心要点和下一步建议
        - **Scene分类标准:**
            - **积极合作:** 明确表达合作兴趣（"I'm interested", "Let's do this"）
            - **探索沟通:** 有兴趣但需更多信息（"Tell me more", "What's included?"）
            - **报价回复:** 提供具体价格条件（"My rate is $500"）
            - **确认合作:** 同意条件，准备执行（"Sounds good", "Here's my address"）
            - **样品确认:** 确认收到产品样品（"Package received"）
            - **内容完成:** 视频/内容制作完成（"Video is live"）
            - **婉拒合作:** 明确或委婉拒绝（"Not interested"）
            - **自动回复:** 非本人真实回复（"Out of office"）
            - **未知:** 意图不明确或异常情况
        - **Stage流程定义（不可逆进展）:**
            - 初次联系 → 意图探索 → 价格谈判 → 合作确认 → 样品发送 → 内容创作 → 内容审核 → 项目完成
        - **处理机制:**
            - 每次收到新邮件后自动调用AI分析
            - 在邮件时间线中显示Scene标识和Summary摘要
            - 根据分析结果自动更新建联记录的Stage阶段
            - 项目阶段只能向后推进，绝不可回退到更早阶段
        - **特殊处理:**
            - 当最后邮件意图为"内容完成"，场景是"内容审核"时，在该邮件上显示专属图标【审核完成】
            - 此时自动更新阶段为"项目完成"
        - **[后台需求]** 需要集成LLM服务进行邮件内容分析和意图识别
    - **2.6.8 AI邮件内容生成功能:**
        - **功能概述:** 提供统一的AI邮件生成服务，根据合作阶段、邮件类型和上下文信息自动生成专业英文商务邮件
        - **邮件类型支持:**
            - **first_contact（初次建联）:** 首次联系网红，需要product_info参数
            - **quote_negotiation（价格谈判）:** 回应网红报价，需要budget_range参数
            - **shipping_notice（发货通知）:** 样品寄出通知，需要shipping_info参数
            - **script_suggestion（脚本建议）:** 内容创作指导，需要script_guideline参数
            - **other（自定义邮件）:** 其他业务场景，需要extra_context参数
        - **生成规范:**
            - 全英文输出，语调友好自然
            - 邮件正文≤220英文单词
            - 纯文本格式，不使用Markdown
            - 避免模板化表达和过度营销语言
            - 根据历史记录避免重复内容
        - **上下文感知:**
            - 结合项目当前阶段和历史邮件摘要
            - 自然引用对方邮件内容（≤15英文单词）
            - 根据网红风格和产品特性调整语调
        - **动态内容策略:**
            - 分析历史沟通避免重复介绍
            - 已确认的合作细节无需重复说明
            - 根据合作进度调整邮件重点和结构
        - **输出格式:** 返回包含influencer_id、influencer_name、email_subject、email_body的JSON结构
        - **[后台需求]** 需要集成LLM服务进行个性化邮件内容生成
    - **2.6.9 数据同步需求:**
        - 邮件自动同步: 定期同步Gmail收件箱，识别达人回复
            - 目前是 5 分钟
        - 通知推送: 收到新回复时及时通知用户
        - 数据备份: 所有沟通记录需要可靠备份和导出功能

### 2.7 账户设置 (User Settings)

- **2.7.1 界面:**
    - 以独立页面或全屏模态框形式展示。
    - 包含清晰的段落标题（主账号信息、身份验证、授权服务等）。
- **2.7.2 主账号信息:**
    - 显示用户头像、用户名、邮箱。
    - 提供更换头像功能（需实现图片上传和存储）。
    - 允许编辑用户名。
    - 邮箱（主登录邮箱）通常设为不可更改。
    - 显示账号类型（如：企业账号）和状态（如：已验证）。
- **2.7.3 身份验证:**
    - 列出当前用于登录的身份验证方式（如：Google账号）。
    - 显示关联的邮箱。
    - 提供“断开连接”选项（需考虑对登录的影响，可能需要有其他验证方式时才允许断开）。
- **2.7.4 授权服务 / 关联账号:**
    - 用于管理应用对外部服务（如Gmail）的访问授权。
    - 列出已授权的Gmail账号。
    - **对每个账号:**
        - 显示服务名称（Gmail）、关联邮箱。
        - 标识是否为“主要”发件邮箱。
        - 显示已授予的权限（如：发送邮件、读取邮件）。
        - 提供操作：设为主要、管理权限（跳转Google授权页？）、移除授权。
    - **添加新账号:**
        - 提供“添加新的Gmail账号”按钮。
        - 点击后启动Google OAuth流程，请求所需权限，并将新账号添加到列表。
    - **邮箱配置列表：**
        - 显示所有已配置的邮箱账号（QQ、163、其他、Gmail）
        - 按邮箱类型分组显示：Gmail授权账号、SMTP/IMAP配置账号
    - **邮箱类型选择界面：**
        - 提供4个邮箱类型选项卡片：QQ邮箱、163邮箱、其他邮箱、Gmail
        - 每个卡片显示邮箱图标、名称和配置说明
    - **QQ邮箱配置：**
        - 自动填充服务器参数（smtp.qq.com:465, imap.qq.com:993）
        - 显示配置指导卡片：登录QQ邮箱 → 设置→账户 → 开启IMAP/SMTP服务 → 获取授权码
        - 提供跳转链接：https://mail.qq.com/
        - 用户填写：邮箱地址、授权码、显示名称
    - **163邮箱配置：**
        - 自动填充服务器参数（smtp.163.com:465, imap.163.com:993）
        - 显示配置指导卡片：登录163邮箱 → 设置→POP3/SMTP/IMAP → 开启服务 → 获取授权码
        - 提供跳转链接：https://mail.163.com/
        - 用户填写：邮箱地址、授权码、显示名称
    - **其他邮箱配置：**
        - 用户手动填写所有参数：SMTP/IMAP服务器、端口、加密方式、用户名、密码
        - 提供常见邮箱服务商配置模板
        - 支持连接测试功能
    - **Gmail OAuth配置：**
        - 点击Gmail选项后转换为OAuth授权界面
        - 显示"授权Gmail账号"按钮，跳转Google OAuth授权页面
        - 授权成功后自动配置，支持多Gmail账号管理
        - 显示已授权账号列表，包含权限范围和状态
    - **邮箱管理功能：**
        - **对每个邮箱账号：**
            - 显示邮箱类型、地址、状态（正常/错误/禁用）
            - 标识是否为"主要"发件邮箱
            - 显示最后同步时间和连接状态
            - 提供操作：设为主要、测试连接、编辑配置、删除账号

---

### 3. AI 具体字段+prompt

### **3.1 达人内容》平台账号分析**

**达人内容表**

| **新字段** | **来源字段与处理说明** | **平台** |
| --- | --- | --- |
| 达人名称 | ins.csv -> 达人名称<br>tk_search.csv -> 达人名称<br>ytb_search.csv -> 达人名称 | 所有平台 |
| 内容平台 | 根据来源文件确定： 'ins', 'tiktok', 'youtube' | 所有平台 |
| 内容标题 | ins.csv -> 无直接标题，可用帖子文字开头部分或留空<br>tk_search.csv -> 内容标题<br>ytb_search.csv -> 内容标题 | 所有平台 |
| 推广商品分类 | ins.csv -> 推广商品分类<br>tk_search.csv -> 推广商品分类<br>ytb_search.csv -> 推广商品分类 (这是基于搜索该内容时的标签) | 所有平台 |
| 增强标签 | ins.csv -> 增强标签<br>tk_search.csv -> 增强标签<br>ytb_search.csv -> 增强标签 (同上) | 所有平台 |
| 视频封面图 | ins.csv -> 封面图<br>tk_search.csv -> 视频封面图<br>ytb_search.csv -> 视频封面图 | 所有平台 |
| 视频链接 | ins.csv -> 内容链接<br>tk_search.csv -> 视频链接<br>ytb_search.csv -> 视频链接 | 所有平台 |
| 点赞数 | ins.csv -> 点赞数<br>tk_search.csv -> 点赞数<br>ytb_search.csv -> 点赞数 (需清理文字，如 '1.5万 人赞过' -> 15000) | 所有平台 |
| 评论数 | ins.csv -> 评论数<br>tk_search.csv -> 评论数<br>ytb_search.csv -> 评论数 (需清理文字，如 '查看 153 条评论' -> 153) | 所有平台 |
| 内容发布日期 | ins.csv -> 内容发布日期 (格式需统一)<br>tk_search.csv -> 内容发布日期 (格式需统一)<br>ytb_search.csv -> 内容发布日期 (格式需统一) | 所有平台 |
| 播放量 | **播放量** | 所有平台 |
| 简介 | **内容简介** | 所有平台 |
| 评论 | **热门评论 (最多前三条，每条包含评论内容和点赞数)** | 所有平台 |
| 类型 | **内容类型 (例如: 视频、短视频、图文)** | 所有平台 |

### Prompt

```python
Role:
你是一位专业的社交媒体分析师和市场洞察专家。
任务:
根据提供的某位达人在特定社交媒体平台发布的【内容数据列表】，深入分析并生成该达人在该平台的【平台账号详情】信息。请侧重于分析内容的主题、风格、受众特征、推广行为和整体调性。
输入数据说明:
你将收到一个JSON格式的列表，其中包含该达人在指定平台上发布的多条内容记录。每条记录（代表一个帖子或视频）将包含以下关键字段：
内容标题 (content_title): 帖子的文字内容或视频标题。
推广商品分类 (promo_category): 基于爬虫搜索标签得出的推广品类。
增强标签 (enhanced_tag): 基于爬虫增强标签得出的标签。
视频封面图 (cover_image_url): 封面图链接 (如果AI模型能处理图像，可用于视觉分析)。
视频链接 (content_url): 原始内容链接 (AI模型通常无法直接访问)。
点赞数 (like_count): 内容的点赞数。
评论数 (comment_count): 内容的评论数。
内容发布日期 (publish_date): 内容的发布日期。
播放量 (views_number): 内容的播放量数据。
内容简介 (description): 内容的详细描述或简介文字。
热门评论 (top_comments): 最多前三条热门评论，每条包含评论内容和点赞数。
内容类型 (content_type): 内容类型枚举值 (例如: 视频、短视频、图文)。
# 达人与平台背景:达人名称: [请在此处插入达人名称]
分析平台: [请在此处插入平台名称: 如 youtube, tiktok, ins]
# 输出要求: 请根据输入数据，分析并生成以下字段的信息，并以JSON格式返回结果。对于无法根据现有信息确定的字段，请使用 "无法判断" 或 "信息不足"。
{
  "受众性别": "分析的主要受众性别 (例如：男性为主, 女性为主, 均衡, 无法判断)",
  "受众年龄": "分析的主要受众年龄段 (例如：18-24岁, 25-34岁, 跨度较广, 无法判断)",
  "地区/国家": "根据内容语言、话题、评论等推断的主要受众地区/国家 (例如：美国, 东南亚, 全球英语区, 无法判断)",
  "语言": "内容主要使用的语言 (例如：英语, 西班牙语, 中文, 无法判断)",
  "内容格式": ["列出主要的几种内容格式 (例如：开箱测评, 教程, Vlog, 清单推荐, 短剧, 挑战, 直播切片), 基于标题和内容描述推断"],
  "近期内容总结": "用1-2句话总结最近发布内容的主要特点和主题方向 (重点参考最近3-5条内容)",
  "视频风格": "描述视频的视觉和剪辑风格 (例如：快节奏, 电影感, 简洁明了, 原始粗糙, 动画/特效多, 无法判断 - 主要基于可推断信息)",
  "内容调性": "描述内容的整体感觉和氛围 (例如：专业严肃, 轻松幽默, 温馨治愈, 高级感, 真实生活流, 可爱活泼, ACG二次元, 无法判断)",
  "垂类深度": "评估内容是否高度专注于特定领域 (例如：高度垂直(美妆), 中度垂直(家居生活), 泛领域(日常Vlog), 无法判断)",
  "推广能力评估": "评估达人推广产品的能力和频率 (例如：频繁推广/硬广多, 自然植入/软广为主, 测评类推广, 几乎无推广, 无法判断)",
  "品牌重复率": "评估合作品牌的重复情况 (例如：与少数品牌深度绑定, 合作品牌多样/轮换快, 多为单次推广, 未见明显商业推广, 无法判断)",
  "内容展示场景": ["列出内容中常见的几个主要场景标签 (例如：居家环境, 工作室, 户外旅行, 厨房, 汽车内, 无法判断 - 主要基于可推断信息)"]
}
分析指南:
受众分析: 结合内容主题、语言、热门评论（如果提供）、互动数据等推断。
内容总结: 侧重于近期的内容主题和趋势，可参考内容简介字段。
风格与调性: 从标题、标签、内容简介、(可选的)封面图信息中综合判断。
推广行为: 留意标题/描述中是否提及品牌、折扣码，或出现"合作"、"广告"等字眼。
场景分析: 根据内容描述、简介或标题推断可能的拍摄环境。
内容类型分析: 利用content_type字段区分不同形式的内容（视频、短视频、图文），分析不同类型内容的表现差异。
评论洞察: 通过热门评论分析受众反馈和参与度，了解受众喜好和关注点。

```

### 达人平台账号表

| **新字段** | **来源字段与处理说明** | **平台** |
| --- | --- | --- |
| 达人名称 | ins_user.csv -> 达人名称<br>tk_search_user.csv -> 达人名称 (或 子名称 作参考)<br>ytb_search_user.csv -> 达人名称 | 所有平台 |
| 达人平台 | 根据来源文件确定： 'ins', 'tiktok', 'youtube' | 所有平台 |
| 达人链接 | ins_user.csv -> 达人链接<br>tk_search_user.csv -> 达人链接<br>ytb_search_user.csv -> 达人链接 | 所有平台 |
| 头像 | ins_user.csv -> 头像<br>tk_search_user.csv -> 头像<br>ytb_search_user.csv -> 头像 | 所有平台 |
| 联系方式 | ins_user.csv -> 联系方式 (为空则无)<br>tk_search_user.csv -> 联系方式 (为空则无)<br>ytb_search_user.csv -> 联系方式 (为空则无) | 所有平台 |
| 订阅数 | ins_user.csv -> 粉丝数<br>tk_search_user.csv -> 粉丝数<br>ytb_search_user.csv -> 订阅数 | 所有平台 |
| 累计观看数 | ytb_search_user.csv -> 累计观看数 | Youtube |
| 获赞数 | tk_search_user.csv -> 点赞数 (这是该账户所有视频的总赞数) | Tiktok |
| 互动率 | 需要计算:  (平均点赞数 + 平均评论数) / 订阅数。从 达人内容表 获取该平台所有内容的点赞、评论数据。 | 所有平台 |
| 互动律趋势 | 需要计算:  (最近3条的点赞总和 + 评论总和) / 3 / 订阅数。从 达人内容表 获取数据。同上，分享/收藏可能需省略。 | 所有平台 |
| 推广商品分类 | ins_user.csv -> 推广商品分类<br>tk_search_user.csv -> 推广商品分类<br>ytb_search_user.csv -> 推广商品分类 | 所有平台 |
| 增强标签 | ins_user.csv -> 增强标签<br>tk_search_user.csv -> 增强标签<br>ytb_search_user.csv -> 增强标签 (同上) | 所有平台 |
| 近期视频播放 | 需要计算: 从 达人内容表 筛选该平台近30天发布的视频，计算其播放数 (ytb_search.csv -> 播放数, IG/TK视频需有播放数数据) 的平均值。规则：多于3条取最新3条均值，少于等于3条取实际数量均值，无则为0。 | 所有平台（需视频） |
| 视频发布频率 | 需要计算: (今天日期 - 倒数第2个视频的 内容发布日期) / 2。需要至少2条内容记录。从 达人内容表 获取日期。 | 所有平台 |
| 记录时间 | ins_user.csv -> 记录时间<br>tk_search_user.csv -> 记录时间<br>ytb_search_user.csv -> 记录时间 | 所有平台 |
| 最后更新时间 | 需要获取: 从 达人内容表 获取该平台最新一条内容的 内容发布日期。 | 所有平台 |
| 平台是否活跃 | 需要计算: 根据 最后更新时间 判断，如果 最后更新时间 在过去7天内，则为“是”，否则为“否”。 | 所有平台 |
| 受众性别 | **需要AI分析** | 所有平台 |
| 受众年龄 | **需要AI分析** | 所有平台 |
| 地区/国家 | 需要AI分析: 可参考 ytb_search_user.csv 的 地区 作为输入。 | 所有平台 |
| 语言 | **需要AI分析** | 所有平台 |
| 内容格式 | 需要AI分析/推断: 基于 达人内容表 的 内容标题、标签、 ytb_search.csv 的 类目 等信息进行分析。 | 所有平台 |
| 近期内容总结 | 需要AI分析: 基于 达人内容表 近期内容的 内容标题、标签等信息进行总结。 | 所有平台 |
| 视频风格 | 需要AI分析: 基于视频内容和封面图分析。 | 所有平台（需视频） |
| 内容调性 | 需要AI分析: 基于视频内容、封面图、标题、标签等综合分析。 | 所有平台 |
| 垂类深度 | 需要AI分析/评估: 基于 核心内容方向 的多样性和近期内容的专注度判断。 | 所有平台 |
| 推广能力评估 | 需要AI分析: 分析 达人内容表 中的内容标题和描述，判断提及品牌的频率。 | 所有平台 |
| 品牌重复率 | 需要AI分析: 分析 达人内容表 中推广内容涉及的品牌，判断合作模式。 | 所有平台 |
| 内容展示场景 | 需要AI分析: 分析 达人内容表 中近期视频的视觉内容，识别场景标签。 | 所有平台（需视频） |

### **3.2 达人总评分+带货能力评级**

### Prompt

使用达人平台账号表的内容去生成账号总表

```python
# Role:
你是一位顶尖的跨平台社交媒体分析师和网红营销策略专家，擅长解读和整合多平台的账号指标数据，对网红进行精准的综合价值评估和画像描绘。

# 任务:
根据提供的【指定达人】在【每个关联平台】的【平台账号详情数据列表】，进行全面的跨平台分析和综合评估，直接生成该达人【达人账号总表】所需的以下**特定字段**信息。

# 输入数据说明:

你将收到一个JSON格式的列表，其中包含该达人在**每个关联平台**的**详细账号指标和分析结果** (`平台账号详情数据列表`)。每个对象代表一个平台的数据。**你的所有分析和输出都必须完全基于此列表中的信息。**

关键输入字段**示例** (请根据实际拥有的字段提供):
*   `platform`: 平台名称 (e.g., "youtube", "tiktok", "ins") - **必需字段**
*   `follower_count`: 粉丝/订阅数
*   `interaction_rate`: 平均互动率
*   `is_active_platform`: 平台是否活跃
*   `audience_gender_platform`: 主要受众性别
*   `audience_age_platform`: 主要受众年龄段
*   `audience_region_platform`: 主要受众地区/国家
*   `language_platform`: 主要语言
*   `content_format_platform`: 主要内容格式 (列表)
*   `content_tone_platform`: 内容调性
*   `vertical_depth_platform`: 垂类深度评估 (如: 高度垂直(美妆), 泛领域)
*   `promotion_capability_platform`: 推广能力评估 (如: 频繁推广, 自然植入)
*   `brand_repeat_rate_platform`: 品牌重复率 (如: 深度绑定, 多样轮换)
*   `video_style_platform` (可选): 视频风格
*   `posting_frequency_days` (可选): 平均发布间隔
*   `recent_interaction_trend` (可选): 近期互动趋势
*   `avg_recent_views` (可选): 近期平均播放
*   `total_likes` / `total_views` (可选): 平台总赞/总播放

**# 达人背景:**
*   **达人ID:** [请在此处插入达人ID]
*   **达人主要名称:** [请在此处插入为该达人选择的主要名称]

**# 输出要求:**
请根据输入的 **【平台账号详情数据列表】**，进行跨平台综合分析，并**仅**生成以下【达人账号总表】字段的信息，以**JSON格式**返回结果。

```json
{
  "核心内容方向": [
    "结合所有平台的 `vertical_depth_platform`, `content_format_platform` 等信息，总结该达人最核心的几个内容领域 (列表，不超过5个)"
  ],
  "综合人设/风格": "基于各平台的 `content_tone_platform`, `video_style_platform`, `content_format_platform` 等，概括该达人跨平台的整体形象和风格特点",
  "主要受众画像": "整合各平台的 `audience_gender_platform`, `audience_age_platform`, `audience_region_platform`, `language_platform` 信息，描述最核心的受众群体特征",
  "商业化程度评估": "根据各平台的 `promotion_capability_platform`, `brand_repeat_rate_platform` 信息，评估达人整体的商业推广参与程度 (例如：低度商业化, 中度商业化, 高度商业化, 深度商业绑定)",
  "跨平台内容一致性": "对比各平台的 `vertical_depth_platform`, `content_format_platform`, `content_tone_platform`，评估内容在不同平台是相似还是差异化 (例如：高度一致, 基本一致略有侧重, 平台差异化明显)",
  "潜在合作品牌类型": [
    "基于总结出的 `核心内容方向`, `综合人设/风格`, `主要受众画像`，推测最适合合作的品牌品类 (列表)"
  ],
  "达人总体评价": "综合评估各平台的 `follower_count`, `interaction_rate`, `is_active_platform`, 内容质量指标 (`content_tone_platform`, `vertical_depth_platform` 等)，对达人的跨平台影响力和内容价值给出一个整体性评价",
  "带货能力评级": "基于 `商业化程度评估`，结合各平台影响力指标 (`follower_count`, `interaction_rate`) 和 `核心内容方向` 的带货属性，评估达人整体的带货潜力等级 (例如：高, 中, 低, 无法判断)"
}
# 分析指南:严格依据输入: 所有的结论都必须能从提供的 平台账号详情数据列表 中的字段推导出来。
跨平台整合: 寻找所有平台数据中的共性、主导趋势或显著差异。
核心内容方向: 识别各平台垂类 (vertical_depth_platform) 和格式 (content_format_platform) 的重叠部分或最突出的部分。
人设/风格: 找到各平台调性 (content_tone_platform) 和风格 (video_style_platform) 的共同点，或描述其组合特征。
受众画像: 确定主导的受众特征（性别、年龄、地区、语言），如果平台间差异大，需指出。
商业化程度: 查看各平台的推广评估 (promotion_capability_platform, brand_repeat_rate_platform)，判断整体推广的频率和深度。
内容一致性: 直接比较各平台的内容相关字段（垂类、格式、调性）的相似度。
合作品牌: 根据达人的核心内容、风格和受众，反推哪些品类的品牌会契合。
总体评价: 结合影响力（粉丝、互动）、内容质量（调性、深度）、活跃度进行综合判断，考虑平台权重。
带货评级: 结合商业化程度、影响力、内容相关性进行定级。

```

### **达人账号总表**

此表需要合并所有用户资料文件（`ins_user.csv`, `tk_search_user.csv`, `ytb_search_user.csv`）的信息，并进行分析/计算。

| **字段** | **说明** | **数据来源/生成方式** |
| --- | --- | --- |
| **达人id** | 用于唯一标识网红的ID。 | 手动分配/系统生成 |
| **达人名称** | 综合判定的主要达人名称 (通常为主平台的名称)。 | 规则判定 (见Prompt) |
| **主语言** | 达人内容和沟通的主要语言。 | 规则判定/AI聚合 (见Prompt) |
| **地区** | 达人主要关联或受众所在的地区/国家。 | 规则判定/AI聚合 (见Prompt) |
| **关联平台** | 该达人拥有账号的所有社交媒体平台列表 (如 ["youtube", "tiktok", "ins"])。 | 规则判定 |
| **主要平台** | 根据数据表现判定（互动率）的，该达人最核心、表现最好的平台。 | 规则判定 |
| **互动程度评估** | 主要平台的互动率数值。 | 规则判定 |
| **达人是否活跃** | 综合判断该达人近期是否在任一平台保持活跃。 | 规则判定 |
| 推广商品分类 | 汇总所有平台的推广商品分类，分类来自抖音分类表 |  |
| **联系方式** | 1. 如果各平台联系方式相同，则显示该方式。2. 如果只有一个平台提供联系方式，则显示该方式。3. 如果各平台联系方式不同，则显示 主要平台 的 联系方式。 | 规则判定 (见Prompt) |
| **核心内容方向** | [AI生成] 综合所有平台内容，提炼出的主要内容领域 (列表，如 ["科技测评", "生活技巧", "美妆分享"])。 | AI分析聚合 (见Prompt) |
| **综合人设/风格** | [AI生成] 概括该达人在所有平台的整体形象和风格 (如 "专业科技测评师", "轻松幽默生活博主")。 | AI分析聚合 (见Prompt) |
| **主要受众画像** | [AI生成] 整合所有平台分析结果，描述核心受众特征 (如 "以美国年轻男性为主(18-34岁)")。 | AI分析聚合 (见Prompt) |
| **商业化程度评估** | [AI生成] 评估该达人整体的商业推广参与程度 (如 "中度商业化", "高度商业化")。 | AI分析聚合 (见Prompt) |
| **跨平台内容一致性** | [AI生成] 评估该达人在不同平台发布内容的相似度或差异化程度 (如 "高度一致", "平台差异化明显")。 | AI分析聚合 (见Prompt) |
| **潜在合作品牌类型** | [AI生成] 基于内容、风格和受众，推测适合合作的品牌品类列表 (如 ["数码产品", "快时尚", "生活用品"])。 | AI分析推断 (见Prompt) |
| **达人总体评价** | [AI生成]  "综合评估达人在该平台的内容质量、互动表现、独特性和发展潜力 (例如：内容质量高，互动积极，风格独特，潜力巨大 / 内容同质化，互动一般，有待提升 / 更新频率低，影响力有限 等)", | AI分析聚合 (见Prompt) |
| **带货能力评级** | [AI生成] 评估达人推广产品并促成转化的潜力等级 (例如：高, 中, 低, 无法判断)。评级依据包括：推广频率与自然度、产品与内容的契合度、受众对推广内容的反馈(评论/点赞情况)、内容垂类的购买属性等 | AI分析推断 (见Prompt) |

### **3.3 商品信息标签+受众分析**

### **商品信息表**

[无标题](https://www.notion.so/2160fdd1937c800d8740e6cc0b469de6?pvs=21)

### Prompt

```markdown
角色
你是一名资深电商商品分析师，负责把商品文本转换为结构化标签，并先判断数据有效性。
任务
• 输入：单个商品 JSON。关键字段：
ProductName, Brand, AmazonCategory, Specifications, Description, Price, Rating, ReviewCount（以及可能的其他字段）。
• 步骤：
1. 按下述规则判断商品是否有效。
2. 若有效，提取并返回 FeatureTags / AudienceTags / UsageScenarioTags。
3. 若无效，仅返回 InvalidReason。
商品有效性规则
1) 数据异常
• 所有字段为同一字符 / 字符串（如 “1”“test”“aaa”）。
• ProductName 明显为测试数据或乱码。
• 价格为 0、负数或离谱高价。
2) 内容缺失
• ProductName 为空 / 仅空格。
• 关键字段（ProductName,Description,Specifications）均为空或无效。
3) 逻辑异常
• 名称与描述完全不匹配。
• Brand 为测试数据或乱码。
• 分类与商品内容毫不相关。
输出格式（必须严格匹配）
{
"IsValidProduct": true,
"FeatureTags": ["标签1","标签2"],
"AudienceTags": ["标签A","标签B"],
"UsageScenarioTags": ["场景X","场景Y"],
"InvalidReason": "字段问题说明（仅当 IsValidProduct 为 false 时出现）"
}
字段说明：
• FeatureTags：功能/规格/材质/设计亮点。
• AudienceTags：目标用户群。
• UsageScenarioTags：典型使用场景。
• InvalidReason：用中文界面字段名指出问题；若多字段，同列 20 字内。
• 示例：商品标题、商品价格为空，请填写后重试，需要明确告知用户修改后再重新提交。
标签提取准则（仅当有效）
类型 标准化示例 说明
FeatureTags 大容量存储(64GB), 智能降噪, 超长续航(100h) 用括号标规格；去除营销词；避免重复
AudienceTags 记者,学生,内容创作者 基于功能/场景推断人群
UsageScenarioTags 会议记录,讲座录音,便携拍摄 场景应具体可感知
处理流程
1. 检查有效性 → IsValidProduct。
2. 若有效：解析文本 → 生成三类标签。
3. 若无效：IsValidProduct=false，FeatureTags/AudienceTags/UsageScenarioTags=[]，写 InvalidReason。
⸻
提示：保持键顺序一致，输出无需解释文字，仅返回 JSON。

```

### **3.4 商品与平台达人账号匹配**

记录个 log 表，商品 id 和平台账号总表，记录 ai 返回的的匹配结果

### prompt

```jsx
# Role:
你是一位专业的网红营销匹配专家和品牌策略师，擅长理解商品特性与不同达人风格、受众的契合度，为商品寻找最合适的推广人选。

# 任务:
根据提供的【单一商品信息】和【多个达人账号总表信息列表】，为该商品评估**每一个达人**的匹配度。你需要输出每个达人与该商品的**匹配度评级 (高/中/低)** 以及详细的**推荐理由**，说明匹配或不匹配的关键因素。

# 输入数据说明:

你将收到 **两部分** JSON 格式的输入数据：

**第一部分: 【单一商品信息】 (product_info)**
*   一个包含**单个商品**详细信息的JSON对象。
*   关键字段**建议包含**:
    *   `ProductName`: 商品名称
    *   `PrimaryCategory` / `SecondaryCategory`: 商品分类
    *   `Brand`: 品牌
    *   `Price`: 价格
    *   `FeatureTags`: [列表] 商品核心特征标签
    *   `AudienceTags`: [列表] 商品目标受众标签
    *   `UsageScenarioTags`: [列表] 商品使用场景标签
    *   `Description` : 商品简介
    *   `Specifications` : 商品参数

**第二部分: 【待匹配达人列表】 (influencers_to_match)**
*   一个包含**多个达人**关键特征和评估结果的JSON列表。
*   每个对象代表一个达人。
*   关键字段**建议包含**:
    *   `达人ID` / `达人名称` (influencer_id / influencer_name)
    *   `核心内容方向` (core_content_areas): [列表] 主要内容领域
    *   `综合人设/风格` (persona_style): 整体形象和风格描述
    *   `主要受众画像` (audience_profile): 核心受众特征描述 (年龄、性别、地区、兴趣等)
    *   `商业化程度评估` (commercialization_level): (可选) 达人商业化程度
    *   `带货能力评级` (monetization_rating): (可选) 达人带货潜力评级
    *   `粉丝量级评估` (follower_scale_tier): (可选, 如: 腰部/头部/超头部) 用于判断影响力
    *   `主要平台互动率评估` (engagement_tier): (可选, 如: 高/中/低)

**# 输出要求:**
请针对【待匹配达人列表】中的**每一个达人**，输出一个匹配结果对象。最终返回一个包含所有达人匹配结果的JSON列表。每个匹配结果对象应包含以下字段：

```json
[
  {
    "influencer_id": "[达人ID]",
    "influencer_name": "[达人名称]",
    "match_score": "[88%]", // 匹配度评级
    "match_rationale": "[详细的推荐理由]" // 解释评级的原因
  },
  {
    "influencer_id": "[另一个达人ID]",
    "influencer_name": "[另一个达人名称]",
    "match_score": "[30%]",
    "match_rationale": "[该达人的评分理由]"
  }
  // ... 更多达人的匹配结果
]
# 匹配逻辑与推荐理由撰写指南:核心匹配维度:内容相关性: 达人的 核心内容方向 是否与商品的 PrimaryCategory, SecondaryCategory 或 FeatureTags 高度相关？达人能否自然地将该商品融入其内容创作？
受众契合度: 达人的 主要受众画像 是否与商品的 AudienceTags 或推断出的目标用户高度重合？达人的粉丝是否是该商品的潜在消费者？
人设/风格一致性: 达人的 综合人设/风格 是否与商品的 Brand 形象、FeatureTags 体现的调性（如科技感、居家、时尚）以及 UsageScenarioTags 相符？推广该商品是否符合达人一贯的形象？
场景融入度: 商品的 UsageScenarioTags 是否是达人内容中经常出现或可以自然创造的场景？
根据上述维度评估，计算一个基础分，然后根据调整因素进行微调，最终得出一个 1-10 之间的整数评分。
示例评分区间参考:
8-10分 (高匹配): 核心维度（内容、受众、风格）均高度契合，调整因素无明显负面影响。
5-7分 (中匹配): 核心维度有强有弱，或整体契合度尚可但存在一些次要不匹配因素（如价格）。
1-4分 (低匹配): 核心维度存在明显不匹配或冲突，难以自然推广。撰写推荐理由 (match_rationale):必须具体: 清晰说明评级的依据。明确指出是哪些达人特征（如“其核心内容为家居生活”）与哪些商品属性（如“该商品为智能家居清洁机器人”）相匹配或不匹配。
引用证据: 直接引用输入数据中的关键信息点（如标签、分类、风格描述）来支撑你的判断。
解释“为什么”: 不仅要说匹配/不匹配，更要解释为什么匹配（例如：“因为达人受众主要是追求生活品质的年轻白领，与该高端厨具的目标用户一致”）或为什么不匹配（例如：“虽然产品是科技类，但达人风格偏重娱乐搞笑，强行评测可能效果不佳”）。
考虑优缺点: 对于“中”评级，可以指出匹配的优点和潜在的不足之处。
简洁明了: 理由应逻辑清晰，语言精练。

```

### **3.5 根据达人和商品特点写建联邮件**

### prompt

```jsx
角色

你是资深网红营销专员，擅长写走心且高转化的合作邀请。

任务

读取 【商品信息】(product_info) + 【达人信息】(influencer_info) 两个 JSON，生成 1 封专属建联的英文邮件。

输出

{
  "influencer_id": "...",
  "influencer_name": "...",
  "email_subject": "...",
  "email_body": "..."
}

写信规则

区块        要点
email_subject        必含品牌或品类 + 达人名/关键词；简短、有钩子。例：`[品牌] 新品 × [达人名]
email_body        1. 称呼：Hi/亲爱的 [influencer_name]。2. 真诚认可：一句具体内容举例 + 1 句风格评价，避免空洞套话。3. 品牌&产品：≤2 句介绍 Brand & ProductName，可附 ProductURL。4. 契合点：把 FeatureTags / UsageScenarioTags 对接 达人 core_content_areas / persona_style；再把 AudienceTags 对接 audience_profile。5. 合作提议：开放式（如寄样体验、开箱评测、日常 Vlog 融合等）。6. CTA：邀请回复。7. 收尾：感谢 + 签名。
语气        友好、直接、类真人口吻；少形容词堆叠，避免“很高兴联系您”等模板句；不强行夸张。
语言        用达人的主语言地区细节可微调称呼/礼貌用语。

其他
        •        不使用Markdown 符号。
        •        不生成除 JSON 外的任何文本。
        •        邮件内容务必使用英文

```

### **3.6 达人回复邮件意图识别**

**识别机制说明:**
1. **Scene（邮件场景）:** 标记在具体邮件上，识别当前邮件的主要合作意图
2. **Stage（项目阶段）:** 记录在整个建联记录上，表示项目当前所处的流程节点
3. **Summary（邮件摘要）:** 30-50字自然语言概括，显示在邮件时间线中

**数据更新机制:** 最新邮件分析完成后，自动更新建联记录的整体阶段状态

### Prompt

```bash
你是一名跨境品牌合作流程专家，专注于自动化理解和推进与网红的邮件往来。
任何时候，项目阶段只能向后推进，绝不可回退到更早阶段。

【输入信息】
- 历史邮件（thread_history）
- 当前邮件（current_email）
- 网红档案（influencer_profile）
- 产品信息（product_info）
- 当前阶段（current_project_stage）

【阶段顺序（不可逆）】
1. 初次联系
2. 意图探索
3. 价格谈判
4. 合作确认
5. 样品发送
6. 内容创作
7. 内容审核
8. 项目完成
（如无法判断，用"未知"；但绝不能回到比 current_project_stage 更早的序号）

【任务】
1. scene —— 识别当前邮件的主要合作意图
2. stage —— 在保证"阶段不回退"的前提下，结合全部上下文给出最新项目阶段
3. summary —— 30-50 字中文，自然语言概括邮件要点与下一步建议

【输出格式】仅返回以下 JSON，勿添加其他字符：

```json
{
  "scene": "<积极合作|探索沟通|报价回复|确认合作|样品确认|内容完成|婉拒合作|自动回复|未知>",
  "stage": "<初次联系|意图探索|价格谈判|合作确认|样品发送|内容创作|内容审核|项目完成|未知>",
  "summary": "<30-50字自然语言摘要，含下一步建议>"
}
```

### **3.7 AI邮件内容生成Prompt**

```markdown
你是一名资深网红营销专员，擅长撰写走心且高转化的英文商务邮件。
请根据输入 JSON 生成 **1 封专属英文邮件**，并 **仅以 JSON** 形式输出（禁止输出其它文本）。

---

## 1 输入字段

| 字段 | 必/选 | 说明 |
|------|------|------|
| email_type | **必填** | `"first_contact" \| "quote_negotiation" \| "shipping_notice" \| "script_suggestion" \| "other"` |
| scene | 必填 | 如 `"探索沟通"`、`"报价回复"`…，辅助判断语气 & 重点 |
| stage | 选填 | 如 `"价格谈判"`、`"样品发送"`…，判定合作进度 |
| history_summaries | 必填 | 最近若干封邮件的摘要数组，按时间顺序 |
| current_email | 必填 | 对方最新来信全文，便于引用/回应 |
| influencer_name | **必填** | 达人称呼 |
| influencer_style | 选填 | 达人内容风格 / 主语言 (示例 `"tech review / English"`) |
| product_info | **必填** | 产品卖点、品牌名称，可附 URL |
| budget_range | 选填 | 预算区间；quote_negotiation 场景常用 |
| shipping_info | 选填 | 物流公司 + 单号 + ETA；shipping_notice 场景常用 |
| script_guideline | 选填 | 脚本要求；script_suggestion 场景常用 |
| extra_context | 选填 | 用户自定义补充指令、特殊偏好等 |

---

## 2 输出格式（严格 JSON）

```json
{
  "influencer_id": "<string>",
  "influencer_name": "<string>",
  "email_subject": "<string>",
  "email_body": "<string>"
}
```

## 3 写信动态指南

**整体要求**
• 全英文；语气友好、自然，避免模板化 & 夸张宣传
• email_body ≤ 220 英文词；不使用 Markdown 符号；不得出现 "As an AI" 等表述
• 根据 stage / history_summaries 判断哪些信息已沟通过，避免重复啰嗦
• 如需引用 current_email，务必自然简短（≤ 15 词）

**建议段落结构（按需增删，顺序可调）**

**first_contact（初次建联）**
- 开头：具体夸赞网红内容（1-2句）
- 主体：品牌介绍 + 产品核心卖点
- 契合点：说明为什么选择该网红
- 结尾：询问合作兴趣

**quote_negotiation（价格谈判）**
- 开头：感谢回复并认可网红价值
- 主体：说明预算范围和让步理由
- 价值点：强调长期合作潜力
- 结尾：邀请进一步讨论

**shipping_notice（发货通知）**
- 开头：确认合作并表达期待
- 主体：详细物流信息（快递公司、单号、预计到达）
- 说明：收货注意事项
- 结尾：询问收货确认

**script_suggestion（脚本建议）**
- 开头：确认样品收到
- 主体：2-3个具体的视频创意方向
- 风格：结合网红风格和产品特性
- 结尾：表达期待并提供支持

**other（自定义邮件）**
- 完全基于extra_context中的用户需求
- 保持专业商务邮件格式
- 结合当前项目上下文

按照以上规范生成邮件，并仅输出符合 "输出格式" 的 JSON。
```

### **3.8 独立站商品提取 prompt**

```
请从以下 HTML 页面内容中提取商品信息，返回结构化 JSON 格式，字段结构必须如下（字段名为英文，字段值优先使用中文，保留必要的英文术语如型号名）：

{
  "results": {
    "url": "商品链接",
    "asin": "商品 ASIN（如能提取）",
    "page": 1,
    "brand": "品牌名称",
    "price": 商品价格（数字类型，单位美元，若为其他货币请换算）,
    "stock": "库存描述，例如“目前无货”或“仅剩 3 件”",
    "title": "商品完整标题",
    "coupon": "优惠券信息（如无则留空）",
    "images": ["商品图片链接1", "商品图片链接2", "..."],
    "rating": 评分（数字，如4.3）,
    "reviews": [
      {
        "id": "评论 ID",
        "title": "评论标题",
        "author": "评论人名称",
        "rating": 评分（数字）,
        "content": "评论内容（中文表达优先）",
        "timestamp": "评论时间",
        "profile_id": "用户ID",
        "is_verified": 是否为认证用户（true/false）,
        "review_from": "评论地区来源",
        "helpful_count": 有用数（如有）
      }
    ],
    "category": [
      {
        "ladder": [
          {"url": "分类链接", "name": "分类名称"},
          ...
        ]
      }
    ],
    "currency": "USD",
    "description": "商品介绍（以中文表述为主）",
    "product_details": {
      "item_model_number": "型号",
      "material": "材质",
      "weight": "重量",
      "connectivity_technology": "连接方式",
      "package_dimensions": "包装尺寸",
      ...
    }
  }
}

        •        内容尽量使用中文描述。
        •        reviews 至少提取 3 条高质量评论（长评优先）。
        •        product_details 字段尽可能提取结构化参数。

```

### 待定问题

- 创作者数据的来源和更新频率？
- 通知中心的推送设计？
- ai 分析具体流程，商品管理流程