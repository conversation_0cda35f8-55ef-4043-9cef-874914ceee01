/**
 * 增强版用户引导系统样式
 * 解决视觉问题并提升用户体验
 */

/* 基础引导样式重写 */
.user-guide-driver-enhanced .driver-popover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: #ffffff !important;
    border-radius: 12px !important;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3), 0 0 0 1px rgba(255, 255, 255, 0.1) !important;
    min-width: 320px !important;
    max-width: 420px !important;
    padding: 24px !important;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* 标题样式优化 */
.user-guide-driver-enhanced .driver-popover-title {
    font-size: 22px !important;
    font-weight: 700 !important;
    color: #ffffff !important;
    margin-bottom: 12px !important;
    line-height: 1.3 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

/* 描述文字样式优化 */
.user-guide-driver-enhanced .driver-popover-description {
    font-size: 16px !important;
    line-height: 1.6 !important;
    color: #f8f9fa !important;
    margin-bottom: 20px !important;
    font-weight: 400 !important;
}

.user-guide-driver-enhanced .driver-popover-description strong {
    color: #ffd700 !important;
    font-weight: 600 !important;
}

/* 按钮样式现代化 */
.user-guide-driver-enhanced .driver-popover-footer button {
    background: rgba(255, 255, 255, 0.15) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    border-radius: 8px !important;
    padding: 10px 20px !important;
    font-size: 14px !important;
    font-weight: 600 !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
    text-shadow: none !important;
}

.user-guide-driver-enhanced .driver-popover-footer button:hover {
    background: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.user-guide-driver-enhanced .driver-popover-footer button:active {
    transform: translateY(0) !important;
}

/* 关闭按钮样式 */
.user-guide-driver-enhanced .driver-popover-close-btn {
    color: rgba(255, 255, 255, 0.7) !important;
    font-size: 20px !important;
    width: 36px !important;
    height: 36px !important;
    border-radius: 50% !important;
    background: rgba(255, 255, 255, 0.1) !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.user-guide-driver-enhanced .driver-popover-close-btn:hover {
    color: #ffffff !important;
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.1) !important;
}

/* 进度文字样式 */
.user-guide-driver-enhanced .driver-popover-progress-text {
    color: rgba(255, 255, 255, 0.8) !important;
    font-size: 13px !important;
    font-weight: 500 !important;
}

/* 箭头样式优化 */
.user-guide-driver-enhanced .driver-popover-arrow {
    border-color: #667eea !important;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2)) !important;
}

/* 遮罩层优化 */
.user-guide-driver-enhanced .driver-overlay {
    background: rgba(0, 0, 0, 0.75) !important;
    backdrop-filter: blur(2px) !important;
}

/* 高亮元素边框效果 */
.user-guide-driver-enhanced .driver-active-element {
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.6), 
                0 0 0 8px rgba(102, 126, 234, 0.3),
                0 0 20px rgba(102, 126, 234, 0.4) !important;
    border-radius: 8px !important;
    transition: all 0.3s ease !important;
}

/* 特定步骤的自定义样式 */
.guide-step-welcome .driver-popover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    text-align: center !important;
}

.guide-step-interactive .driver-popover {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
    border-left: 4px solid #ffd700 !important;
}

.guide-step-interactive .driver-popover-title::before {
    content: "🎯 ";
    font-size: 24px !important;
}

.guide-step-complete .driver-popover {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
    text-align: center !important;
}

/* 交互式步骤的特殊样式 */
.interactive-step-highlight {
    animation: pulse-highlight 2s infinite !important;
}

@keyframes pulse-highlight {
    0%, 100% {
        box-shadow: 0 0 0 4px rgba(255, 215, 0, 0.6) !important;
    }
    50% {
        box-shadow: 0 0 0 8px rgba(255, 215, 0, 0.8) !important;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .user-guide-driver-enhanced .driver-popover {
        min-width: 280px !important;
        max-width: 320px !important;
        padding: 20px !important;
    }
    
    .user-guide-driver-enhanced .driver-popover-title {
        font-size: 20px !important;
    }
    
    .user-guide-driver-enhanced .driver-popover-description {
        font-size: 15px !important;
    }
}

@media (max-width: 480px) {
    .user-guide-driver-enhanced .driver-popover {
        min-width: 260px !important;
        max-width: 300px !important;
        padding: 16px !important;
    }
    
    .user-guide-driver-enhanced .driver-popover-title {
        font-size: 18px !important;
    }
    
    .user-guide-driver-enhanced .driver-popover-description {
        font-size: 14px !important;
    }
}

/* 动画效果增强 */
.user-guide-driver-enhanced .driver-popover {
    animation: slideInScale 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
}

@keyframes slideInScale {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(20px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 菜单项交互高亮样式 */
.guide-menu-item-highlight {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0.1)) !important;
    border-left: 4px solid #667eea !important;
    animation: menu-item-pulse 1.5s ease-in-out infinite !important;
    transition: all 0.3s ease !important;
}

@keyframes menu-item-pulse {
    0%, 100% {
        background: linear-gradient(90deg, rgba(102, 126, 234, 0.2), rgba(102, 126, 234, 0.1)) !important;
    }
    50% {
        background: linear-gradient(90deg, rgba(102, 126, 234, 0.3), rgba(102, 126, 234, 0.2)) !important;
    }
}

/* 页面切换时的过渡效果 */
.guide-page-transition {
    transition: opacity 0.3s ease-in-out !important;
}

.guide-page-transition.fade-out {
    opacity: 0.3 !important;
}

.guide-page-transition.fade-in {
    opacity: 1 !important;
}
